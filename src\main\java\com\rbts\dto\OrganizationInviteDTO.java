package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrganizationInviteDTO {

    private Long id;
    
    @NotBlank(message = "Email is required")
    @Email(message = "Valid email is required")
    @JsonProperty("email")
    private String email;
    
    // Response fields from Calendly
    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("organization")
    private String organization; // Organization URI
    
    @JsonProperty("status")
    private String status; // pending, accepted, declined
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("last_sent_at")
    private LocalDateTime lastSentAt;
    
    @JsonProperty("user")
    private UserDTO user;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserDTO {
        @JsonProperty("uri")
        private String uri;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("slug")
        private String slug;
        
        @JsonProperty("email")
        private String email;
        
        @JsonProperty("scheduling_url")
        private String schedulingUrl;
        
        @JsonProperty("timezone")
        private String timezone;
        
        @JsonProperty("avatar_url")
        private String avatarUrl;
        
        @JsonProperty("created_at")
        private LocalDateTime createdAt;
        
        @JsonProperty("updated_at")
        private LocalDateTime updatedAt;
    }
}
