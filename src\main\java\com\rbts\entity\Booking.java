package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@Entity
@Table(name = "bookings")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Booking {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bookingSeqGen")
    @SequenceGenerator(name = "bookingSeqGen", sequenceName = "bookings_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "session_id", nullable = false)
    private Session session;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "contact_id", nullable = false)
    private ContactDetails students;

    @NotNull
    @OneToOne
    @JoinColumn(name = "status_id", nullable = false)
    private StatusMaster status;

    @NotNull
    @Column(name = "booked_at", nullable = false)
    private LocalDateTime bookedAt;

    @Column(name = "credit_points_used")
    private Long creditPointsUsed;

    @OneToOne
    @JoinColumn(name = "recurrence_pattern_id")
    private SessionRecurrencePattern recurrencePattern;
}
