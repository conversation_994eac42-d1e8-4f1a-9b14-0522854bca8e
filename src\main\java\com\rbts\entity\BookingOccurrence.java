package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "booking_occurrences")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BookingOccurrence {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bookingOccurrenceSeqGen")
    @SequenceGenerator(name = "bookingOccurrenceSeqGen", sequenceName = "booking_occurrence_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "booking_id", nullable = false)
    private Booking booking;

    @NotNull
    @Column(name = "occurrence_time", nullable = false)
    private LocalDateTime occurrenceTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @OneToOne
    @JoinColumn(name = "status_id")
    private StatusMaster statusId;
}
