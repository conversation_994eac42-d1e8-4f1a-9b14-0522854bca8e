package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyAvailabilityResponse {

    @JsonProperty("collection")
    private List<CalendlyAvailabilitySlot> collection;
    
    @JsonProperty("pagination")
    private PaginationResponse pagination;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PaginationResponse {
        @JsonProperty("count")
        private Integer count;
        
        @JsonProperty("next_page")
        private String nextPage;
        
        @JsonProperty("previous_page")
        private String previousPage;
        
        @JsonProperty("next_page_token")
        private String nextPageToken;
        
        @JsonProperty("previous_page_token")
        private String previousPageToken;
    }
}


