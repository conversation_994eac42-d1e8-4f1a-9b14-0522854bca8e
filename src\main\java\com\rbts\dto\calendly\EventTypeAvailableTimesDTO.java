package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventTypeAvailableTimesDTO {

    @JsonProperty("collection")
    private List<AvailableTime> collection;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AvailableTime {
        
        @JsonProperty("invitees_remaining")
        private Integer inviteesRemaining;
        
        @JsonProperty("scheduling_url")
        private String schedulingUrl;
        
        @JsonProperty("start_time")
        private String startTime; // "2025-06-26T05:00:00Z"
        
        @JsonProperty("status")
        private String status; // "available"
    }
}
