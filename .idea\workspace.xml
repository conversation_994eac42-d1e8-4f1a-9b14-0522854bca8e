<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c3ef03ee-ec39-468b-9869-9e95c8d1e0fd" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rbts/entity/Session.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rbts/entity/Session.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2ywPwCWb0ZpnqMjjRosbLZfnAXb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.LocateTutor.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.AuthenticationServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/RedberyltTech-Locate-Tutor/Locate-Tutor-1&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Maven.LocateTutor">
    <configuration name="LocateTutor" type="MavenRunConfiguration" factoryName="Maven">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings">
          <MavenRunnerSettings>
            <option name="delegateBuildToMaven" value="false" />
            <option name="environmentProperties">
              <map>
                <entry key="DB_HOST" value="localhost" />
                <entry key="DB_NAME" value="locate_tutor" />
                <entry key="DB_PASSWORD" value="root" />
                <entry key="DB_PORT" value="5432" />
                <entry key="DB_USERNAME" value="postgres" />
              </map>
            </option>
            <option name="jreName" value="#USE_PROJECT_JDK" />
            <option name="mavenProperties">
              <map />
            </option>
            <option name="passParentEnv" value="true" />
            <option name="runMavenInBackground" value="true" />
            <option name="skipTests" value="false" />
            <option name="vmOptions" value="" />
          </MavenRunnerSettings>
        </option>
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="clean" />
                <option value="install" />
                <option value="spring-boot:run" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <configuration name="AuthenticationServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="locate-tutor-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.rbts.AuthenticationServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Maven.LocateTutor" />
      <item itemvalue="Spring Boot.AuthenticationServiceApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c3ef03ee-ec39-468b-9869-9e95c8d1e0fd" name="Changes" comment="" />
      <created>1750742899309</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750742899309</updated>
      <workItem from="1750742901204" duration="22129000" />
      <workItem from="1750821921377" duration="26806000" />
      <workItem from="1750859142137" duration="2519000" />
      <workItem from="1750866771535" duration="4199000" />
      <workItem from="1750900748254" duration="3652000" />
      <workItem from="1750907853495" duration="14562000" />
      <workItem from="1750953577804" duration="2888000" />
      <workItem from="1750998680607" duration="37000" />
      <workItem from="1751008069155" duration="1330000" />
      <workItem from="1751177803407" duration="4007000" />
      <workItem from="1751195078675" duration="5628000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>