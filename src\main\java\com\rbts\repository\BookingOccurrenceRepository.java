package com.rbts.repository;

import com.rbts.entity.BookingOccurrence;
import com.rbts.entity.Booking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingOccurrenceRepository extends JpaRepository<BookingOccurrence, Long> {

    List<BookingOccurrence> findByBooking(Booking booking);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking.id = :bookingId")
    List<BookingOccurrence> findByBookingId(@Param("bookingId") Long bookingId);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.occurrenceTime >= :startTime AND bo.occurrenceTime <= :endTime")
    List<BookingOccurrence> findOccurrencesInTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking.session.tutorId.id = :tutorId AND bo.occurrenceTime >= :startTime AND bo.occurrenceTime <= :endTime")
    List<BookingOccurrence> findTutorOccurrencesInTimeRange(@Param("tutorId") Long tutorId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking.students.id = :studentId AND bo.occurrenceTime >= :startTime AND bo.occurrenceTime <= :endTime")
    List<BookingOccurrence> findStudentOccurrencesInTimeRange(@Param("studentId") Long studentId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.statusId.id = :statusId")
    List<BookingOccurrence> findByStatusId(@Param("statusId") Long statusId);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking.id = :bookingId AND bo.occurrenceTime = :occurrenceTime")
    Optional<BookingOccurrence> findByBookingIdAndOccurrenceTime(@Param("bookingId") Long bookingId, @Param("occurrenceTime") LocalDateTime occurrenceTime);
}
