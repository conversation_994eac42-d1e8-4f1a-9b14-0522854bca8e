# 🔐 Calendly OAuth 2.0 Implementation Guide

## 📋 Overview

This guide shows how to migrate from personal access tokens to OAuth 2.0 authentication for production-ready Calendly integration with proper user consent flow.

## 🎯 Benefits of OAuth 2.0

### **Current State (Personal Access Token):**
- ❌ Static token in configuration
- ❌ No user consent flow
- ❌ Limited to one Calendly account
- ❌ Not suitable for multi-tenant applications
- ❌ Security concerns with shared tokens

### **OAuth 2.0 Benefits:**
- ✅ Dynamic token generation per user
- ✅ User consent flow with proper permissions
- ✅ Support for multiple Calendly accounts
- ✅ Secure token refresh mechanism
- ✅ Production-ready authentication
- ✅ Granular scope-based permissions

## 🏗️ Architecture Components

### **1. Database Schema**
```sql
CREATE TABLE calendly_oauth_tokens (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    calendly_user_uri VARCHAR(255) UNIQUE,
    calendly_user_email VARCHAR(255),
    calendly_user_name VARCHAR(255),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    token_type VARCHAR(50) NOT NULL,
    scope TEXT,
    expires_at TIMESTAMP,
    organization_uri VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL,
    last_refreshed_at TIMESTAMP
);
```

### **2. Configuration**
```properties
# OAuth 2.0 Configuration (already in application.properties)
calendly.oauth.client-id=m9lm-dJ8xDZzYvSDY1WhqQgzr2hgHI9Op9aS7UXxXXM
calendly.oauth.client-secret=rDoddcrJbBi-jzemAG89dqfYc5BsDl88w6NaivMeuKM
calendly.oauth.redirect-uri=http://localhost:9766/api/calendly/oauth/callback
calendly.oauth.scope=default
calendly.api.url=https://api.calendly.com

# Fallback Personal Access Token (for backward compatibility)
calendly.api.token=calendly.api.token
```

## 🚀 OAuth 2.0 Flow Implementation

### **Step 1: User Authorization**

```bash
# Generate authorization URL
GET /api/calendly/oauth/authorize?userId=123&scope=default

# Response:
{
  "status": "success",
  "authorization_url": "https://auth.calendly.com/oauth/authorize?client_id=...&response_type=code&redirect_uri=...&scope=default&state=...",
  "instructions": "Redirect user to this URL to grant Calendly permissions"
}
```

### **Step 2: User Grants Permission**
User is redirected to Calendly's authorization server and grants permissions.

### **Step 3: Authorization Code Exchange**

```bash
# Calendly redirects back to your callback URL
GET /api/calendly/oauth/callback?code=AUTH_CODE&state=STATE&userId=123

# Response:
{
  "status": "success",
  "message": "OAuth authorization completed successfully",
  "user_id": 123,
  "calendly_user_uri": "https://api.calendly.com/users/USER_UUID",
  "calendly_user_email": "<EMAIL>",
  "calendly_user_name": "John Doe",
  "organization_uri": "https://api.calendly.com/organizations/ORG_UUID",
  "scope": "default",
  "expires_at": "2025-06-30T10:00:00"
}
```

## 🔧 Implementation Details

### **1. Token Management**

#### **Automatic Token Refresh:**
```java
// Service automatically refreshes tokens when needed
String accessToken = oauthService.getValidAccessToken(userId);
// Returns fresh token, refreshing if necessary
```

#### **Manual Token Refresh:**
```bash
POST /api/calendly/oauth/refresh?userId=123
```

#### **Token Revocation:**
```bash
POST /api/calendly/oauth/revoke?userId=123
```

### **2. Service Layer Integration**

The `CalendlyService` now supports both OAuth and personal access tokens:

```java
// Use OAuth token for specific user
private HttpHeaders getHeaders(Long userId) {
    String accessToken = oauthService.getValidAccessToken(userId);
    return getHeaders(accessToken);
}

// Fallback to personal access token
private HttpHeaders getHeaders() {
    String personalToken = getPersonalAccessToken();
    return getHeaders(personalToken);
}
```

### **3. Multi-User Support**

Each user can have their own Calendly OAuth token:

```java
// User 1 creates a session using their OAuth token
SessionDTO session1 = calendlyService.createSession(sessionDTO, userId1);

// User 2 creates a session using their OAuth token  
SessionDTO session2 = calendlyService.createSession(sessionDTO, userId2);
```

## 🔄 Migration Strategy

### **Phase 1: Dual Mode Support**
- ✅ Keep existing personal access token functionality
- ✅ Add OAuth 2.0 support alongside
- ✅ Allow gradual user migration

### **Phase 2: OAuth Adoption**
- 🔄 Encourage users to authorize via OAuth
- 🔄 Provide migration tools and documentation
- 🔄 Monitor adoption rates

### **Phase 3: OAuth Only**
- 🎯 Deprecate personal access token usage
- 🎯 Require OAuth for all new users
- 🎯 Full production-ready authentication

## 📊 Usage Examples

### **Frontend Integration**

```javascript
// Step 1: Get authorization URL
const authResponse = await fetch('/api/calendly/oauth/authorize?userId=123');
const authData = await authResponse.json();

// Step 2: Redirect user to Calendly
window.location.href = authData.authorization_url;

// Step 3: Handle callback (Calendly redirects back)
// Your callback endpoint processes the code and stores the token

// Step 4: Check token status
const statusResponse = await fetch('/api/calendly/oauth/status?userId=123');
const statusData = await statusResponse.json();

if (statusData.has_valid_token) {
    // User is authorized, can use Calendly features
    console.log('User has valid Calendly authorization');
} else {
    // Need to authorize
    console.log('User needs to authorize Calendly access');
}
```

### **Backend Service Usage**

```java
// Check if user has OAuth token
@GetMapping("/sessions")
public ResponseEntity<List<SessionDTO>> getUserSessions(@RequestParam Long userId) {
    try {
        // This will use OAuth token if available, fallback to personal token
        List<SessionDTO> sessions = calendlyService.getUserSessions(userId);
        return ResponseEntity.ok(sessions);
    } catch (Exception e) {
        if (e.getMessage().contains("No OAuth token found")) {
            // Redirect to OAuth authorization
            return ResponseEntity.status(401).body("OAuth authorization required");
        }
        throw e;
    }
}
```

## 🔍 Token Status Monitoring

### **Check Token Status:**
```bash
GET /api/calendly/oauth/status?userId=123

# Response for valid token:
{
  "status": "success",
  "has_valid_token": true,
  "token_preview": "eyJraWQiOi..."
}

# Response for invalid/missing token:
{
  "status": "error",
  "has_valid_token": false,
  "message": "No OAuth token found for user. Please authorize first."
}
```

## 🎯 Production Readiness Checklist

- ✅ **OAuth 2.0 Flow**: Complete authorization code flow
- ✅ **Token Storage**: Secure database storage with encryption
- ✅ **Token Refresh**: Automatic refresh mechanism
- ✅ **Multi-User Support**: Per-user token management
- ✅ **Error Handling**: Comprehensive error handling
- ✅ **Security**: CSRF protection with state parameter
- ✅ **Monitoring**: Token status and health checks
- ✅ **Backward Compatibility**: Fallback to personal tokens

## 🚀 Next Steps

1. **Database Migration**: Add OAuth token table
2. **User Interface**: Build OAuth authorization flow in frontend
3. **Testing**: Test OAuth flow with multiple users
4. **Monitoring**: Set up token expiry monitoring
5. **Documentation**: Update API documentation
6. **Migration**: Plan migration from personal tokens

Your application is now ready for production-grade OAuth 2.0 authentication with Calendly! 🎉
