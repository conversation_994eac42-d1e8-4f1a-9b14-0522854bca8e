package com.rbts.entity;

import com.rbts.entity.master.SubjectMaster;
import com.rbts.entity.master.SubjectSubCategory;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "session_enquiry")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionEnquiry {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sessionEnquirySeqGen")
    @SequenceGenerator(name = "sessionEnquirySeqGen", sequenceName = "session_enquiry_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "student_id", nullable = false)
    private ContactDetails student;

    @ManyToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutor;

    @NotNull
    @OneToOne
    @JoinColumn(name = "subject_id", nullable = false)
    private SubjectMaster subject;

    @OneToOne
    @JoinColumn(name = "subject_sub_category_id")
    private SubjectSubCategory subjectSubCategory;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "duration_min")
    private Long durationMin;

    @Column(name = "enquiry_status")
    private String enquiryStatus;

    @Column(name = "subject_description", columnDefinition = "TEXT")
    private String subjectDescription;

    @Column(name = "tutor_notes", columnDefinition = "TEXT")
    private String tutorNotes;
}
