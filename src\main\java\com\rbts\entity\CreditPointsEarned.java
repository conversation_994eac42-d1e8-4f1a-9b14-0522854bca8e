package com.rbts.entity;

import com.rbts.entity.master.CreditPointSource;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "credit_points_earned")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreditPointsEarned {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "creditPointsEarnedSeqGen")
    @SequenceGenerator(name = "creditPointsEarnedSeqGen", sequenceName = "credit_points_earned_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "student_id", nullable = false)
    private ContactDetails student;

    @NotNull
    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @NotNull
    @OneToOne
    @JoinColumn(name = "credit_point_source_id", nullable = false)
    private CreditPointSource creditPointSource;

    @NotNull
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
}
