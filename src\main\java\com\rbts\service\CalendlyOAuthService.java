package com.rbts.service;

import com.rbts.dto.calendly.CalendlyOAuthDTO;
import com.rbts.entity.CalendlyOAuthToken;
import com.rbts.repository.CalendlyOAuthTokenRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class CalendlyOAuthService {

    @Value("${calendly.oauth.client-id}")
    private String clientId;

    @Value("${calendly.oauth.client-secret}")
    private String clientSecret;

    @Value("${calendly.oauth.redirect-uri}")
    private String redirectUri;

    @Value("${calendly.oauth.scope:default}")
    private String defaultScope;

    @Value("${calendly.api.base-url}")
    private String calendlyApiUrl;

    @Value("${calendly.oauth.authorization-url:https://auth.calendly.com/oauth/authorize}")
    private String authorizationUrl;

    @Value("${calendly.oauth.token-url:https://auth.calendly.com/oauth/token}")
    private String tokenUrl;

    @Autowired
    private CalendlyOAuthTokenRepository tokenRepository;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * Generate OAuth authorization URL for user to grant permissions
     */
    public String generateAuthorizationUrl(Long userId, String scope) {
        String state = generateState(userId);
        String actualScope = scope != null ? scope : defaultScope;
        
        return authorizationUrl + 
               "?client_id=" + clientId +
               "&response_type=code" +
               "&redirect_uri=" + redirectUri +
               "&scope=" + actualScope +
               "&state=" + state;
    }

    /**
     * Exchange authorization code for access token
     */
    @Transactional
    public CalendlyOAuthToken exchangeCodeForToken(String code, String state, Long userId) {
        try {
            // Validate state parameter
            if (!validateState(state, userId)) {
                throw new RuntimeException("Invalid state parameter - possible CSRF attack");
            }

            // Prepare token request
            CalendlyOAuthDTO.TokenRequest tokenRequest = CalendlyOAuthDTO.TokenRequest.builder()
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .code(code)
                    .grantType("authorization_code")
                    .redirectUri(redirectUri)
                    .build();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CalendlyOAuthDTO.TokenRequest> request = new HttpEntity<>(tokenRequest, headers);

            // Exchange code for token
            ResponseEntity<CalendlyOAuthDTO.TokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, CalendlyOAuthDTO.TokenResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to exchange code for token");
            }

            CalendlyOAuthDTO.TokenResponse tokenResponse = response.getBody();

            // Fetch user info
            CalendlyOAuthDTO.UserInfo userInfo = fetchUserInfo(tokenResponse.getAccessToken());

            // Deactivate existing tokens for this user
            deactivateExistingTokens(userId);

            // Save new token
            CalendlyOAuthToken oauthToken = CalendlyOAuthToken.builder()
                    .userId(userId)
                    .calendlyUserUri(userInfo.getUri())
                    .calendlyUserEmail(userInfo.getEmail())
                    .calendlyUserName(userInfo.getName())
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType(tokenResponse.getTokenType())
                    .scope(tokenResponse.getScope())
                    .organizationUri(userInfo.getCurrentOrganization())
                    .expiresAt(calculateExpiryTime(tokenResponse.getExpiresIn()))
                    .isActive(true)
                    .build();

            return tokenRepository.save(oauthToken);

        } catch (Exception e) {
            throw new RuntimeException("Failed to exchange code for token: " + e.getMessage(), e);
        }
    }

    /**
     * Refresh access token using refresh token
     */
    @Transactional
    public CalendlyOAuthToken refreshToken(Long userId) {
        try {
            CalendlyOAuthToken existingToken = tokenRepository.findByUserIdAndIsActiveTrue(userId)
                    .orElseThrow(() -> new RuntimeException("No active token found for user"));

            if (existingToken.getRefreshToken() == null) {
                throw new RuntimeException("No refresh token available");
            }

            CalendlyOAuthDTO.RefreshTokenRequest refreshRequest = CalendlyOAuthDTO.RefreshTokenRequest.builder()
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .refreshToken(existingToken.getRefreshToken())
                    .grantType("refresh_token")
                    .build();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CalendlyOAuthDTO.RefreshTokenRequest> request = new HttpEntity<>(refreshRequest, headers);

            ResponseEntity<CalendlyOAuthDTO.TokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, CalendlyOAuthDTO.TokenResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to refresh token");
            }

            CalendlyOAuthDTO.TokenResponse tokenResponse = response.getBody();

            // Update existing token
            existingToken.setAccessToken(tokenResponse.getAccessToken());
            if (tokenResponse.getRefreshToken() != null) {
                existingToken.setRefreshToken(tokenResponse.getRefreshToken());
            }
            existingToken.setExpiresAt(calculateExpiryTime(tokenResponse.getExpiresIn()));

            return tokenRepository.save(existingToken);

        } catch (Exception e) {
            throw new RuntimeException("Failed to refresh token: " + e.getMessage(), e);
        }
    }

    /**
     * Get valid access token for user (refresh if needed)
     */
    public String getValidAccessToken(Long userId) {
        CalendlyOAuthToken token = tokenRepository.findByUserIdAndIsActiveTrue(userId)
                .orElseThrow(() -> new RuntimeException("No OAuth token found for user. Please authorize first."));

        if (token.needsRefresh() && token.getRefreshToken() != null) {
            token = refreshToken(userId);
        } else if (token.isExpired()) {
            throw new RuntimeException("Token expired and no refresh token available. Please re-authorize.");
        }

        return token.getAccessToken();
    }

    /**
     * Revoke OAuth token
     */
    @Transactional
    public void revokeToken(Long userId) {
        try {
            CalendlyOAuthToken token = tokenRepository.findByUserIdAndIsActiveTrue(userId)
                    .orElseThrow(() -> new RuntimeException("No active token found for user"));

            // Revoke token with Calendly
            CalendlyOAuthDTO.RevokeTokenRequest revokeRequest = CalendlyOAuthDTO.RevokeTokenRequest.builder()
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .token(token.getAccessToken())
                    .build();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CalendlyOAuthDTO.RevokeTokenRequest> request = new HttpEntity<>(revokeRequest, headers);

            restTemplate.postForEntity(tokenUrl.replace("/token", "/revoke"), request, Void.class);

            // Deactivate local token
            token.setIsActive(false);
            tokenRepository.save(token);

        } catch (Exception e) {
            throw new RuntimeException("Failed to revoke token: " + e.getMessage(), e);
        }
    }

    // Helper methods

    private String generateState(Long userId) {
        return Base64.getEncoder().encodeToString((userId + ":" + System.currentTimeMillis()).getBytes());
    }

    private boolean validateState(String state, Long userId) {
        try {
            String decoded = new String(Base64.getDecoder().decode(state));
            return decoded.startsWith(userId + ":");
        } catch (Exception e) {
            return false;
        }
    }

    private LocalDateTime calculateExpiryTime(Integer expiresIn) {
        if (expiresIn == null) {
            return LocalDateTime.now().plusHours(2); // Default 2 hours
        }
        return LocalDateTime.now().plusSeconds(expiresIn);
    }

    private void deactivateExistingTokens(Long userId) {
        List<CalendlyOAuthToken> existingTokens = tokenRepository.findByUserIdAndIsActiveTrueOrderByCreatedAtDesc(userId);
        for (CalendlyOAuthToken token : existingTokens) {
            token.setIsActive(false);
        }
        tokenRepository.saveAll(existingTokens);
    }

    private CalendlyOAuthDTO.UserInfo fetchUserInfo(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        HttpEntity<Void> request = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                calendlyApiUrl + "/users/me", HttpMethod.GET, request, Map.class);

        if (response.getBody() != null && response.getBody().containsKey("resource")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> resource = (Map<String, Object>) response.getBody().get("resource");

            return CalendlyOAuthDTO.UserInfo.builder()
                    .uri((String) resource.get("uri"))
                    .name((String) resource.get("name"))
                    .email((String) resource.get("email"))
                    .schedulingUrl((String) resource.get("scheduling_url"))
                    .timezone((String) resource.get("timezone"))
                    .currentOrganization((String) resource.get("current_organization"))
                    .build();
        }

        throw new RuntimeException("Failed to fetch user info");
    }
}
