package com.rbts.controller;

import com.rbts.entity.CalendlyOAuthToken;
import com.rbts.service.CalendlyOAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/calendly/oauth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CalendlyOAuthController {

    @Autowired
    private CalendlyOAuthService oauthService;

    /**
     * Step 1: Generate OAuth authorization URL for user to grant permissions
     */
    @GetMapping("/authorize")
    public ResponseEntity<Map<String, Object>> getAuthorizationUrl(
            @RequestParam Long userId,
            @RequestParam(required = false) String scope) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String authUrl = oauthService.generateAuthorizationUrl(userId, scope);
            
            response.put("status", "success");
            response.put("message", "Authorization URL generated successfully");
            response.put("authorization_url", authUrl);
            response.put("instructions", "Redirect user to this URL to grant Calendly permissions");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to generate authorization URL: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Step 2: OAuth callback endpoint - exchange authorization code for access token
     */
    @GetMapping("/callback")
    public ResponseEntity<Map<String, Object>> handleOAuthCallback(
            @RequestParam String code,
            @RequestParam String state,
            @RequestParam Long userId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            CalendlyOAuthToken token = oauthService.exchangeCodeForToken(code, state, userId);
            
            response.put("status", "success");
            response.put("message", "OAuth authorization completed successfully");
            response.put("user_id", token.getUserId());
            response.put("calendly_user_uri", token.getCalendlyUserUri());
            response.put("calendly_user_email", token.getCalendlyUserEmail());
            response.put("calendly_user_name", token.getCalendlyUserName());
            response.put("organization_uri", token.getOrganizationUri());
            response.put("scope", token.getScope());
            response.put("expires_at", token.getExpiresAt());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "OAuth callback failed: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Refresh OAuth access token
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshToken(@RequestParam Long userId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            CalendlyOAuthToken token = oauthService.refreshToken(userId);
            
            response.put("status", "success");
            response.put("message", "Token refreshed successfully");
            response.put("expires_at", token.getExpiresAt());
            response.put("last_refreshed_at", token.getLastRefreshedAt());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to refresh token: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Revoke OAuth access token
     */
    @PostMapping("/revoke")
    public ResponseEntity<Map<String, Object>> revokeToken(@RequestParam Long userId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            oauthService.revokeToken(userId);
            
            response.put("status", "success");
            response.put("message", "Token revoked successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to revoke token: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Check OAuth token status for user
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getTokenStatus(@RequestParam Long userId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String accessToken = oauthService.getValidAccessToken(userId);
            
            response.put("status", "success");
            response.put("message", "Valid OAuth token found");
            response.put("has_valid_token", true);
            response.put("token_preview", accessToken.substring(0, Math.min(10, accessToken.length())) + "...");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
            response.put("has_valid_token", false);
            return ResponseEntity.badRequest().body(response);
        }
    }
}
