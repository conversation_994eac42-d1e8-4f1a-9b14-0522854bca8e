package com.rbts.entity;

import com.rbts.entity.master.BillingRateType;
import com.rbts.entity.master.SubjectMaster;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Table(name = "tutor_subjects")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorSubject {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorSubjectSeqGen")
    @SequenceGenerator(name = "tutorSubjectSeqGen", sequenceName = "tutor_subjects_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutorId;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "subject_id", nullable = false)
    private SubjectMaster subject;

    @Column(name = "video_intro_url")
    private String videoIntroUrl;

    @Column(name = "search_tags", columnDefinition = "TEXT")
    private String searchTags;

    @Column(name = "about_subject", columnDefinition = "TEXT")
    private String aboutSubject;

    @Column(name = "fees_rate")
    private BigDecimal feesRate;

    @OneToOne
    @JoinColumn(name = "billing_rate_type_id")
    private BillingRateType billingRateType;

    @Column(name = "final_fees")
    private BigDecimal finalFees;

    @Column(name = "currency_code", length = 3)
    private String currencyCode;
}
