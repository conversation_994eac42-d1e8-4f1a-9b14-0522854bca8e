package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyEventTypeResponse {

    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("active")
    private boolean active;
    
    @JsonProperty("slug")
    private String slug;
    
    @JsonProperty("scheduling_url")
    private String schedulingUrl;
    
    @JsonProperty("duration")
    private Integer duration;
    
    @JsonProperty("kind")
    private String kind;
    
    @JsonProperty("pooling_type")
    private String poolingType;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("color")
    private String color;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("internal_note")
    private String internalNote;
    
    @JsonProperty("description_plain")
    private String descriptionPlain;
    
    @JsonProperty("description_html")
    private String descriptionHtml;
    
    @JsonProperty("profile")
    private ProfileResponse profile;
    
    @JsonProperty("secret")
    private Boolean secret;
    
    @JsonProperty("booking_method")
    private String bookingMethod;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProfileResponse {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("owner")
        private String owner;
    }
}
