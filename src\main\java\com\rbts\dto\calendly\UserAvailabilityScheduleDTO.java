package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAvailabilityScheduleDTO {

    @JsonProperty("collection")
    private List<AvailabilitySchedule> collection;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AvailabilitySchedule {
        
        @JsonProperty("default")
        private Boolean isDefault;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("rules")
        private List<AvailabilityRule> rules;
        
        @JsonProperty("timezone")
        private String timezone;
        
        @JsonProperty("uri")
        private String uri;
        
        @JsonProperty("user")
        private String user;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AvailabilityRule {
        
        @JsonProperty("type")
        private String type; // "wday" or "date"
        
        @JsonProperty("wday")
        private String wday; // "monday", "tuesday", etc.
        
        @JsonProperty("date")
        private String date; // "2025-06-26"
        
        @JsonProperty("intervals")
        private List<TimeInterval> intervals;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TimeInterval {
        
        @JsonProperty("from")
        private String from; // "09:00"
        
        @JsonProperty("to")
        private String to; // "17:00"
    }
}
