package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyInviteResponse {

    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("organization")
    private String organization;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("status")
    private String status; // pending, accepted, declined
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("last_sent_at")
    private LocalDateTime lastSentAt;
    
    @JsonProperty("user")
    private UserResponse user;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserResponse {
        @JsonProperty("uri")
        private String uri;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("slug")
        private String slug;
        
        @JsonProperty("email")
        private String email;
        
        @JsonProperty("scheduling_url")
        private String schedulingUrl;
        
        @JsonProperty("timezone")
        private String timezone;
        
        @JsonProperty("avatar_url")
        private String avatarUrl;
        
        @JsonProperty("created_at")
        private LocalDateTime createdAt;
        
        @JsonProperty("updated_at")
        private LocalDateTime updatedAt;
    }
}
