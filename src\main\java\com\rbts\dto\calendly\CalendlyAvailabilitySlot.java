package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyAvailabilitySlot {

    @JsonProperty("start_time")
    private LocalDateTime startTime;
    
    @JsonProperty("invitees_remaining")
    private Integer inviteesRemaining;
    
    @JsonProperty("status")
    private String status;
}
