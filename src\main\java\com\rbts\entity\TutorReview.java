package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "tutor_reviews")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorReview {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorReviewSeqGen")
    @SequenceGenerator(name = "tutorReviewSeqGen", sequenceName = "tutor_reviews_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "booking_id", nullable = false)
    private Booking booking;

    @NotNull
    @Column(name = "rating", nullable = false)
    private Long rating;

    @Column(name = "comment", columnDefinition = "TEXT")
    private String comment;

    @Column(name = "is_approved")
    private Boolean isApproved;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @NotNull
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    private ContactDetails student;

    @Column(name = "tutor_response", columnDefinition = "TEXT")
    private String tutorResponse;
}
