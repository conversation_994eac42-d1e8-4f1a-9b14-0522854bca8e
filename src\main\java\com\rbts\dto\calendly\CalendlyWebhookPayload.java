package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyWebhookPayload {

    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("created_by")
    private String createdBy;
    
    @JsonProperty("event")
    private String event;
    
    @JsonProperty("payload")
    private PayloadData payload;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PayloadData {
        @JsonProperty("email")
        private String email;
        
        @JsonProperty("organization")
        private String organization;
        
        @JsonProperty("role")
        private String role;
        
        @JsonProperty("user")
        private String user;
        
        @JsonProperty("created_at")
        private LocalDateTime createdAt;
        
        @JsonProperty("updated_at")
        private LocalDateTime updatedAt;
    }
}
