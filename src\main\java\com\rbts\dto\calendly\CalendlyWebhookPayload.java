package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyWebhookPayload {

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("event")
    private String event;

    @JsonProperty("payload")
    private PayloadData payload;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PayloadData {
        // For organization.membership.created event
        @JsonProperty("email")
        private String email;

        @JsonProperty("organization")
        private String organization;

        @JsonProperty("role")
        private String role;

        @JsonProperty("user")
        private String user;

        @JsonProperty("created_at")
        private String createdAt;

        @JsonProperty("updated_at")
        private String updatedAt;

        // For invitee.created/canceled events
        @JsonProperty("uri")
        private String uri;

        @JsonProperty("name")
        private String name;

        @JsonProperty("status")
        private String status;

        @JsonProperty("start_time")
        private String startTime;

        @JsonProperty("end_time")
        private String endTime;

        @JsonProperty("event_type")
        private String eventType;

        @JsonProperty("location")
        private Object location;

        @JsonProperty("invitees_counter")
        private Object inviteesCounter;

        @JsonProperty("questions_and_answers")
        private Object questionsAndAnswers;

        @JsonProperty("tracking")
        private Object tracking;

        @JsonProperty("utm_campaign")
        private String utmCampaign;

        @JsonProperty("utm_source")
        private String utmSource;

        @JsonProperty("utm_medium")
        private String utmMedium;

        @JsonProperty("utm_content")
        private String utmContent;

        @JsonProperty("utm_term")
        private String utmTerm;

        @JsonProperty("cancel_url")
        private String cancelUrl;

        @JsonProperty("reschedule_url")
        private String rescheduleUrl;

        @JsonProperty("old_invitee")
        private String oldInvitee;

        @JsonProperty("new_invitee")
        private String newInvitee;

        @JsonProperty("cancellation")
        private Object cancellation;
    }
}
