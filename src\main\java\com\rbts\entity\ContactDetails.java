package com.rbts.entity;


import com.rbts.encryption.Encrypt;
import com.rbts.encryption.EncryptionEntityListener;
import com.rbts.entity.auth.Users;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Entity
@Table(name = "contacts_details")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EntityListeners(EncryptionEntityListener.class)
public class ContactDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "contactsequencegenerator")
    @SequenceGenerator(name = "contactsequencegenerator", sequenceName = "contactsequencegenerator",allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "salutation")
    private String salutation;

    @NotNull
    @Column(name = "first_name", nullable = false, length = 50)
    private String firstName;

    @NotNull
    @Column(name = "last_name", nullable = false, length = 50)
    private String lastName;

    @NotNull
    @Column(name = "display_name", nullable = false, length = 50)
    private String displayName;

    @Column(name = "avatar_img_path", columnDefinition = "TEXT")
    private String avatarImgPath;

    @Encrypt
    @Column(name = "phone")
    private String phone;

    @Encrypt
    @NotNull
    @Column(name = "email_id", nullable = false, unique = true,columnDefinition = "TEXT", updatable = false)
    private String emailId;

    @Encrypt
    @Column(name = "mobile_no")
    private String mobileNo;

    @Column(name = "trust_pilot_id")
    private String trustPilotId;

    @Column(name = "timezone")
    private LocalDateTime timezone;

    @Column(name = "address",columnDefinition = "TEXT")
    private String address;

    @Column(name = "about_me",columnDefinition = "TEXT")
    private String aboutMe;

    @Column(name = "bio_cover_letter",columnDefinition = "TEXT")
    private String bioCoverLetter;

    @Column(name = "locate_tutor_badge")
    private String locateTutorBadge;

    @Column(name = "locate_tutor_summary",columnDefinition = "TEXT")
    private String locateTutorSummary;

    @Column(name = "gender_id")
    private String genderId;

    @Column(name = "current_location")
    private String currentLocation;

    @Column(name = "geo_location")
    private String geoLocation;

    @Column(name = "is_public")
    private Boolean isPublic;

    @Column(name = "is_verified")
    private Boolean isVerified;

    @Column(name = "willing_to_travel")
    private Boolean willing_to_travel;

    @Column(name = "is_recommended")
    private Boolean isRecommended;

    @Column(name = "ready_for_group_sessions")
    private Boolean readyForGroupSessions;

    // Calendly Integration Fields
    @Column(name = "calendly_user_uri")
    private String calendlyUserUri;

    @Column(name = "calendly_organization_uri")
    private String calendlyOrganizationUri;

    @Column(name = "calendly_role")
    private String calendlyRole;

    @Column(name = "calendly_membership_created_at")
    private LocalDateTime calendlyMembershipCreatedAt;

    @Column(name = "is_calendly_member")
    private Boolean isCalendlyMember;

    @Column(name = "calendly_scheduling_url")
    private String calendlySchedulingUrl;

    @Column(name = "calendly_timezone")
    private String calendlyTimezone;

    @Column(name = "calendly_avatar_url")
    private String calendlyAvatarUrl;

    @ManyToOne
    @NotNull
    @JoinColumn(name = "contact_status_id" )
    private StatusMaster statusId;

    @OneToOne
    @NotNull
    @JoinColumn(name = "user_id",nullable = false, unique = true,updatable = false)
    private Users userId;



}
