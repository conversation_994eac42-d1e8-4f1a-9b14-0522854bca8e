package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "student_favorite_tutor")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StudentFavoriteTutor {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "studentFavTutorSeqGen")
    @SequenceGenerator(name = "studentFavTutorSeqGen", sequenceName = "student_favorite_tutor_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "student_id", nullable = false)
    private ContactDetails student;

    @NotNull
    @OneToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutor;

    @Column(name = "is_favorite")
    private Boolean isFavorite;
}
