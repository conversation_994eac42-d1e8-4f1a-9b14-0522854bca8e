package com.rbts.repository;

import com.rbts.entity.StatusMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StatusMasterRepository extends JpaRepository<StatusMaster, Long> {

    Optional<StatusMaster> findByStatusName(String statusName);
    
    List<StatusMaster> findByIsActiveTrue();
    
    @Query("SELECT sm FROM StatusMaster sm WHERE sm.statusName = :statusName AND sm.isActive = true")
    Optional<StatusMaster> findActiveByStatusName(@Param("statusName") String statusName);
    
    @Query("SELECT sm FROM StatusMaster sm WHERE sm.statusForId.id = :statusForId AND sm.isActive = true")
    List<StatusMaster> findActiveByStatusForId(@Param("statusForId") Long statusForId);
    
    @Query("SELECT sm FROM StatusMaster sm WHERE sm.isActive = true ORDER BY sm.statusName")
    List<StatusMaster> findAllActiveOrderByName();
    
    boolean existsByStatusName(String statusName);
}
