package com.rbts.repository;

import com.rbts.entity.Session;
import com.rbts.entity.ContactDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SessionRepository extends JpaRepository<Session, Long> {

    List<Session> findByTutorId(ContactDetails tutor);
    
    @Query("SELECT s FROM Session s WHERE s.tutorId.id = :tutorId")
    List<Session> findByTutorIdValue(@Param("tutorId") Long tutorId);
    
    @Query("SELECT s FROM Session s WHERE s.eventTypeUri = :eventTypeUri")
    Optional<Session> findByEventTypeUri(@Param("eventTypeUri") String eventTypeUri);
    
    @Query("SELECT s FROM Session s WHERE s.active = true AND s.tutorId.id = :tutorId")
    List<Session> findActiveSessionsByTutorId(@Param("tutorId") Long tutorId);
    
    @Query("SELECT s FROM Session s WHERE s.startTime >= :startTime AND s.endTime <= :endTime")
    List<Session> findSessionsInTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT s FROM Session s WHERE s.tutorId.id = :tutorId AND s.startTime >= :startTime AND s.endTime <= :endTime")
    List<Session> findTutorSessionsInTimeRange(@Param("tutorId") Long tutorId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
