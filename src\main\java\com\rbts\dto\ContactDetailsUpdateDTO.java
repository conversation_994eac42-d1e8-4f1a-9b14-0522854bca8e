package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContactDetailsUpdateDTO {

    private Long id;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("calendly_user_uri")
    private String calendlyUserUri;
    
    @JsonProperty("calendly_organization_uri")
    private String calendlyOrganizationUri;
    
    @JsonProperty("calendly_role")
    private String calendlyRole;
    
    @JsonProperty("calendly_membership_created_at")
    private LocalDateTime calendlyMembershipCreatedAt;
    
    @JsonProperty("is_calendly_member")
    private Boolean isCalendlyMember;
    
    @JsonProperty("calendly_verified")
    private Boolean calendlyVerified;
    
    // Additional Calendly user details (fetched from user URI)
    @JsonProperty("calendly_name")
    private String calendlyName;
    
    @JsonProperty("calendly_slug")
    private String calendlySlug;
    
    @JsonProperty("calendly_scheduling_url")
    private String calendlySchedulingUrl;
    
    @JsonProperty("calendly_timezone")
    private String calendlyTimezone;
    
    @JsonProperty("calendly_avatar_url")
    private String calendlyAvatarUrl;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
}
