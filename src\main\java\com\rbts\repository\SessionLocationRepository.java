package com.rbts.repository;

import com.rbts.entity.SessionLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SessionLocationRepository extends JpaRepository<SessionLocation, Long> {

    Optional<SessionLocation> findByLocationKey(String locationKey);
    
    List<SessionLocation> findByIsActiveTrue();
    
    @Query("SELECT sl FROM SessionLocation sl WHERE sl.locationKey = :locationKey AND sl.isActive = true")
    Optional<SessionLocation> findActiveByLocationKey(@Param("locationKey") String locationKey);
    
    @Query("SELECT sl FROM SessionLocation sl WHERE sl.isActive = true ORDER BY sl.locationKey")
    List<SessionLocation> findAllActiveOrderByKey();
    
    boolean existsByLocationKey(String locationKey);
}
