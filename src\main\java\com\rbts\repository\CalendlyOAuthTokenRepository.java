package com.rbts.repository;

import com.rbts.entity.CalendlyOAuthToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CalendlyOAuthTokenRepository extends JpaRepository<CalendlyOAuthToken, Long> {

    Optional<CalendlyOAuthToken> findByUserIdAndIsActiveTrue(Long userId);
    
    Optional<CalendlyOAuthToken> findByCalendlyUserUriAndIsActiveTrue(String calendlyUserUri);
    
    Optional<CalendlyOAuthToken> findByCalendlyUserEmailAndIsActiveTrue(String calendlyUserEmail);
    
    List<CalendlyOAuthToken> findByUserIdAndIsActiveTrueOrderByCreatedAtDesc(Long userId);
    
    List<CalendlyOAuthToken> findByIsActiveTrueAndExpiresAtBefore(LocalDateTime dateTime);
    
    @Query("SELECT t FROM CalendlyOAuthToken t WHERE t.isActive = true AND t.expiresAt < :expiryTime")
    List<CalendlyOAuthToken> findTokensNeedingRefresh(@Param("expiryTime") LocalDateTime expiryTime);
    
    @Query("SELECT t FROM CalendlyOAuthToken t WHERE t.userId = :userId AND t.isActive = true ORDER BY t.createdAt DESC")
    Optional<CalendlyOAuthToken> findLatestActiveTokenByUserId(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(t) FROM CalendlyOAuthToken t WHERE t.isActive = true")
    Long countActiveTokens();
    
    @Query("SELECT COUNT(t) FROM CalendlyOAuthToken t WHERE t.userId = :userId AND t.isActive = true")
    Long countActiveTokensByUserId(@Param("userId") Long userId);
    
    boolean existsByUserIdAndIsActiveTrue(Long userId);
    
    boolean existsByCalendlyUserUriAndIsActiveTrue(String calendlyUserUri);
    
    void deleteByUserIdAndIsActiveTrue(Long userId);
}
