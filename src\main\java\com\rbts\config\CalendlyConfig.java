package com.rbts.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class CalendlyConfig {

    @Bean
    public RestTemplate calendlyRestTemplate(RestTemplateBuilder builder) {
        return builder.connectTimeout(Duration.ofSeconds(30)).readTimeout(Duration.ofSeconds(60))
                .errorHandler(new CalendlyErrorHandler())
                .interceptors(calendlyInterceptors())
                .build();
    }

    private List<ClientHttpRequestInterceptor> calendlyInterceptors() {
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        
        // Add logging interceptor
        interceptors.add((request, body, execution) -> {
            System.out.println("Calendly API Request: " + request.getMethod() + " " + request.getURI());
            System.out.println("Request Headers: " + request.getHeaders());
            
            ClientHttpResponse response = execution.execute(request, body);
            
            System.out.println("Calendly API Response Status: " + response.getStatusCode());
            System.out.println("Response Headers: " + response.getHeaders());
            
            return response;
        });
        
        return interceptors;
    }

    private static class CalendlyErrorHandler implements ResponseErrorHandler {
        
        @Override
        public boolean hasError(ClientHttpResponse response) throws IOException {
            return response.getStatusCode().is4xxClientError() || 
                   response.getStatusCode().is5xxServerError();
        }

        @Override
        public void handleError(ClientHttpResponse response) throws IOException {
            String statusText = response.getStatusText();
            int statusCode = response.getStatusCode().value();
            
            switch (statusCode) {
                case 400:
                    throw new CalendlyApiException("Bad Request: " + statusText, statusCode);
                case 401:
                    throw new CalendlyApiException("Unauthorized: Invalid or expired token", statusCode);
                case 403:
                    throw new CalendlyApiException("Forbidden: Insufficient permissions", statusCode);
                case 404:
                    throw new CalendlyApiException("Not Found: Resource does not exist", statusCode);
                case 422:
                    throw new CalendlyApiException("Unprocessable Entity: Validation failed", statusCode);
                case 429:
                    throw new CalendlyApiException("Rate Limit Exceeded: Too many requests", statusCode);
                case 500:
                    throw new CalendlyApiException("Internal Server Error: Calendly service error", statusCode);
                default:
                    throw new CalendlyApiException("Calendly API Error: " + statusText, statusCode);
            }
        }
    }

    public static class CalendlyApiException extends RuntimeException {
        private final int statusCode;

        public CalendlyApiException(String message, int statusCode) {
            super(message);
            this.statusCode = statusCode;
        }

        public int getStatusCode() {
            return statusCode;
        }
    }
}
