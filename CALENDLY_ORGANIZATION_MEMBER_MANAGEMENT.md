# 🏢 Calendly Organization Member Management System

## 📋 Overview

This system manages Calendly organization members using a dedicated `CalendlyOrganizationMemberDetails` entity that tracks invitation status, user details, and availability settings for each member.

## 🎯 Key Features

- **Member Tracking**: Track organization invitations and member status
- **Contact Integration**: Link Calendly members to your existing contacts
- **Availability Management**: Store and sync availability settings
- **Invitation Lifecycle**: Track from invitation to acceptance
- **User Details Sync**: Automatically fetch and update user information from Calendly

## 🗄️ Database Schema

### CalendlyOrganizationMemberDetails Table

```sql
CREATE TABLE calendly_organization_member_details (
    id BIGINT PRIMARY KEY,
    organization_uri VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL,
    invitation_uri TEXT,
    invitation_status VARCHAR(50), -- pending, accepted, declined
    user_uri VARCHAR(255) NOT NULL UNIQUE,
    user_email VARCHAR(255) NOT NULL UNIQUE,
    user_name VARCHAR(255),
    scheduling_url TEXT UNIQUE,
    slug VARCHAR(255) UNIQUE,
    timezone VARCHAR(100),
    locale VARCHAR(50),
    time_notation VARCHAR(20),
    active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Availability Fields
    availability_schedule_uri TEXT,
    default_schedule_uri TEXT,
    availability_rules TEXT, -- JSON string
    buffer_time_before INTEGER, -- minutes
    buffer_time_after INTEGER, -- minutes
    minimum_scheduling_notice INTEGER, -- minutes
    maximum_events_per_day INTEGER,
    date_overrides TEXT, -- JSON string
    last_availability_sync TIMESTAMP,
    
    -- Contact Link
    contact_id BIGINT UNIQUE REFERENCES contact_details(id),
    
    -- Audit Fields
    created_date TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_date TIMESTAMP,
    last_modified_by VARCHAR(255)
);
```

## 🔄 Workflow

### 1. **Organization Invitation Flow**

```mermaid
sequenceDiagram
    participant Admin
    participant API
    participant Calendly
    participant Database
    participant User

    Admin->>API: POST /api/calendly/organization/invite
    API->>Calendly: Create organization invitation
    Calendly->>API: Return invitation details
    API->>Database: Create CalendlyOrganizationMemberDetails (status: pending)
    API->>Database: Link to existing ContactDetails if found
    API->>Admin: Return invitation details
    
    User->>Calendly: Accept invitation
    Admin->>API: POST /api/calendly/organization/members/update-acceptance
    API->>Calendly: Fetch user details
    API->>Database: Update member details (status: accepted)
    API->>Admin: Confirmation
```

### 2. **Member Status Tracking**

- **pending**: Invitation sent, waiting for acceptance
- **accepted**: User accepted invitation and joined organization
- **declined**: User declined the invitation

## 🚀 API Endpoints

### **Organization Invitations**

```bash
# Create organization invitation
POST /api/calendly/organization/invite
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```

### **Member Management**

```bash
# Get all organization members
GET /api/calendly/organization/members?organizationUri=https://api.calendly.com/organizations/ORG_UUID

# Get active members only
GET /api/calendly/organization/members/active?organizationUri=https://api.calendly.com/organizations/ORG_UUID

# Get member by email
GET /api/calendly/organization/members/by-email?email=<EMAIL>

# Get member by contact ID
GET /api/calendly/organization/members/by-contact/123

# Update member when they accept invitation
POST /api/calendly/organization/members/update-acceptance
{
  "userEmail": "<EMAIL>",
  "userUri": "https://api.calendly.com/users/USER_UUID"
}
```

## 💻 Usage Examples

### **1. Create Invitation and Track Member**

```java
// Create invitation
OrganizationInviteDTO inviteDTO = OrganizationInviteDTO.builder()
    .email("<EMAIL>")
    .firstName("Jane")
    .lastName("Smith")
    .build();

OrganizationInviteDTO result = calendlyService.createOrganizationInvite(inviteDTO);
// This automatically creates CalendlyOrganizationMemberDetails with status "pending"
```

### **2. Check Member Status**

```java
// Get member details
Optional<CalendlyOrganizationMemberDetails> member = 
    calendlyService.getMemberByEmail("<EMAIL>");

if (member.isPresent()) {
    String status = member.get().getInvitationStatus();
    System.out.println("Invitation status: " + status);
}
```

### **3. Update Member on Acceptance**

```java
// When user accepts invitation (you can call this manually or via webhook if available)
calendlyService.updateMemberDetailsOnAcceptance(
    "<EMAIL>", 
    "https://api.calendly.com/users/USER_UUID"
);
// This fetches and updates user details from Calendly
```

### **4. Get Organization Members**

```java
// Get all active members
List<CalendlyOrganizationMemberDetails> activeMembers = 
    calendlyService.getActiveOrganizationMembers("https://api.calendly.com/organizations/ORG_UUID");

// Check member details
for (CalendlyOrganizationMemberDetails member : activeMembers) {
    System.out.println("Member: " + member.getUserName());
    System.out.println("Email: " + member.getUserEmail());
    System.out.println("Scheduling URL: " + member.getSchedulingUrl());
    System.out.println("Status: " + member.getInvitationStatus());
}
```

## 🔧 Availability Management

### **Store Availability Settings**

```java
CalendlyOrganizationMemberDetails member = // get member
member.setBufferTimeBefore(15); // 15 minutes before
member.setBufferTimeAfter(10);  // 10 minutes after
member.setMinimumSchedulingNotice(1440); // 24 hours in minutes
member.setMaximumEventsPerDay(8);

// Store availability rules as JSON
String availabilityRules = """
{
  "monday": {"start": "09:00", "end": "17:00"},
  "tuesday": {"start": "09:00", "end": "17:00"},
  "wednesday": {"start": "09:00", "end": "17:00"},
  "thursday": {"start": "09:00", "end": "17:00"},
  "friday": {"start": "09:00", "end": "17:00"}
}
""";
member.setAvailabilityRules(availabilityRules);

calendlyMemberRepository.save(member);
```

## 📊 Monitoring and Analytics

### **Member Statistics**

```java
// Count active members
Long activeCount = calendlyMemberRepository.countActiveByOrganizationUri(organizationUri);

// Count accepted invitations
Long acceptedCount = calendlyMemberRepository.countAcceptedByOrganizationUri(organizationUri);

// Get members by role
List<CalendlyOrganizationMemberDetails> tutors = 
    calendlyMemberRepository.findActiveByOrganizationUriAndRole(organizationUri, "tutor");
```

## 🔍 Benefits

### **1. Complete Member Lifecycle Tracking**
- Track from invitation to active membership
- Monitor invitation acceptance rates
- Manage member roles and permissions

### **2. Contact Integration**
- Link Calendly members to existing contacts
- Maintain data consistency across systems
- Enable cross-system reporting

### **3. Availability Management**
- Store member-specific availability settings
- Track availability rule changes
- Sync with Calendly availability schedules

### **4. Scalable Architecture**
- Dedicated entity for Calendly-specific data
- Clean separation from core contact management
- Easy to extend with additional Calendly features

## 🎯 Next Steps

1. **Implement availability sync** with Calendly's availability API
2. **Add role-based permissions** for different member types
3. **Create member dashboard** for viewing organization statistics
4. **Add notification system** for invitation status changes
5. **Implement bulk member operations** for large organizations

This system provides a robust foundation for managing Calendly organization members while maintaining clean data architecture! 🚀
