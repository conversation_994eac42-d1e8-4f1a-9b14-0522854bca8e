package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "tutor_recommendations")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorRecommendation {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorRecommendationSeqGen")
    @SequenceGenerator(name = "tutorRecommendationSeqGen", sequenceName = "tutor_recommendation_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutor;

    @NotNull
    @OneToOne
    @JoinColumn(name = "student_id", nullable = false)
    private ContactDetails student;

    @Column(name = "recommendation_desc", columnDefinition = "TEXT")
    private String recommendationDesc;

    @Column(name = "emotional_rating")
    private String emotionalRating;
}
