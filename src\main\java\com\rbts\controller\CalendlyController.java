package com.rbts.controller;

import com.rbts.dto.SessionDTO;
import com.rbts.dto.BookingDTO;
import com.rbts.dto.AvailabilityDTO;
import com.rbts.dto.OrganizationInviteDTO;
import com.rbts.service.CalendlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

@RestController
@RequestMapping("/api/calendly")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CalendlyController {

    @Autowired
    private CalendlyService calendlyService;

    // 1. CRUD Operations for Session (EventType)
    
    /**
     * Create a new session with Calendly event type
     */
    @PostMapping("/sessions")
    public ResponseEntity<SessionDTO> createSession(@Valid @RequestBody SessionDTO sessionDTO) {
        SessionDTO createdSession = calendlyService.createSession(sessionDTO);
        return ResponseEntity.ok(createdSession);
    }

    /**
     * Get session by ID with Calendly event type details
     */
    @GetMapping("/sessions/{id}")
    public ResponseEntity<SessionDTO> getSession(@PathVariable Long id) {
        SessionDTO session = calendlyService.getSession(id);
        return ResponseEntity.ok(session);
    }

    /**
     * Get all sessions for a tutor
     */
    @GetMapping("/sessions/tutor/{tutorId}")
    public ResponseEntity<List<SessionDTO>> getTutorSessions(@PathVariable Long tutorId) {
        List<SessionDTO> sessions = calendlyService.getTutorSessions(tutorId);
        return ResponseEntity.ok(sessions);
    }

    /**
     * Update session and corresponding Calendly event type
     */
    @PutMapping("/sessions/{id}")
    public ResponseEntity<SessionDTO> updateSession(@PathVariable Long id, @Valid @RequestBody SessionDTO sessionDTO) {
        SessionDTO updatedSession = calendlyService.updateSession(id, sessionDTO);
        return ResponseEntity.ok(updatedSession);
    }

    /**
     * Delete session and corresponding Calendly event type
     */
    @DeleteMapping("/sessions/{id}")
    public ResponseEntity<Void> deleteSession(@PathVariable Long id) {
        calendlyService.deleteSession(id);
        return ResponseEntity.noContent().build();
    }

    // 2. Create Organization Invite
    
    /**
     * Create organization invite for user
     */
    @PostMapping("/organization/invite")
    public ResponseEntity<OrganizationInviteDTO> createOrganizationInvite(@Valid @RequestBody OrganizationInviteDTO inviteDTO) {
        OrganizationInviteDTO createdInvite = calendlyService.createOrganizationInvite(inviteDTO);
        return ResponseEntity.ok(createdInvite);
    }

    /**
     * Get organization invite status
     */
    @GetMapping("/organization/invite/{inviteUri}")
    public ResponseEntity<OrganizationInviteDTO> getOrganizationInvite(@PathVariable String inviteUri) {
        OrganizationInviteDTO invite = calendlyService.getOrganizationInvite(inviteUri);
        return ResponseEntity.ok(invite);
    }

    // 3. Complete User Registration (Verify Calendly Invite Acceptance)
    
    /**
     * Complete user registration after Calendly invite acceptance
     */
    @PostMapping("/users/{userId}/complete-registration")
    public ResponseEntity<String> completeUserRegistration(@PathVariable Long userId, @RequestParam String inviteUri) {
        calendlyService.completeUserRegistration(userId, inviteUri);
        return ResponseEntity.ok("User registration completed successfully");
    }

    /**
     * Check user registration status
     */
    @GetMapping("/users/{userId}/registration-status")
    public ResponseEntity<Boolean> checkRegistrationStatus(@PathVariable Long userId) {
        boolean isComplete = calendlyService.isUserRegistrationComplete(userId);
        return ResponseEntity.ok(isComplete);
    }

    // 4. Override Availability Calendar
    
    /**
     * Override availability calendar for a user
     */
    @PostMapping("/availability/override")
    public ResponseEntity<String> overrideAvailability(@Valid @RequestBody AvailabilityDTO availabilityDTO) {
        calendlyService.overrideAvailability(availabilityDTO);
        return ResponseEntity.ok("Availability overridden successfully");
    }

    /**
     * Get user's availability schedule
     */
    @GetMapping("/availability/{userId}")
    public ResponseEntity<AvailabilityDTO> getUserAvailability(@PathVariable Long userId) {
        AvailabilityDTO availability = calendlyService.getUserAvailability(userId);
        return ResponseEntity.ok(availability);
    }

    // 5. Get Tutor Availability
    
    /**
     * Get tutor availability for specified date and time range
     */
    @GetMapping("/tutors/{tutorId}/availability")
    public ResponseEntity<List<AvailabilityDTO>> getTutorAvailability(
            @PathVariable Long tutorId,
            @RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        List<AvailabilityDTO> availability = calendlyService.getTutorAvailability(tutorId, startTime, endTime);
        return ResponseEntity.ok(availability);
    }

    /**
     * Get available time slots for a specific event type
     */
    @GetMapping("/event-types/{eventTypeUri}/available-times")
    public ResponseEntity<List<AvailabilityDTO>> getEventTypeAvailability(
            @PathVariable String eventTypeUri,
            @RequestParam LocalDateTime startTime,
            @RequestParam LocalDateTime endTime) {
        List<AvailabilityDTO> availability = calendlyService.getEventTypeAvailability(eventTypeUri, startTime, endTime);
        return ResponseEntity.ok(availability);
    }

    // 6. Schedule/Enroll Student for Session
    
    /**
     * Schedule/enroll student for session
     */
    @PostMapping("/bookings")
    public ResponseEntity<BookingDTO> scheduleSession(@Valid @RequestBody BookingDTO bookingDTO) {
        BookingDTO scheduledBooking = calendlyService.scheduleSession(bookingDTO);
        return ResponseEntity.ok(scheduledBooking);
    }

    /**
     * Get booking details
     */
    @GetMapping("/bookings/{bookingId}")
    public ResponseEntity<BookingDTO> getBooking(@PathVariable Long bookingId) {
        BookingDTO booking = calendlyService.getBooking(bookingId);
        return ResponseEntity.ok(booking);
    }

    /**
     * Get all bookings for a student
     */
    @GetMapping("/bookings/student/{studentId}")
    public ResponseEntity<List<BookingDTO>> getStudentBookings(@PathVariable Long studentId) {
        List<BookingDTO> bookings = calendlyService.getStudentBookings(studentId);
        return ResponseEntity.ok(bookings);
    }

    /**
     * Get all bookings for a tutor
     */
    @GetMapping("/bookings/tutor/{tutorId}")
    public ResponseEntity<List<BookingDTO>> getTutorBookings(@PathVariable Long tutorId) {
        List<BookingDTO> bookings = calendlyService.getTutorBookings(tutorId);
        return ResponseEntity.ok(bookings);
    }

    /**
     * Cancel a booking
     */
    @DeleteMapping("/bookings/{bookingId}")
    public ResponseEntity<String> cancelBooking(@PathVariable Long bookingId, @RequestParam(required = false) String reason) {
        calendlyService.cancelBooking(bookingId, reason);
        return ResponseEntity.ok("Booking cancelled successfully");
    }

    // OAuth and Webhook endpoints
    
    /**
     * OAuth callback endpoint
     */
    @GetMapping("/oauth/callback")
    public ResponseEntity<String> oauthCallback(@RequestParam String code, @RequestParam(required = false) String state) {
        String accessToken = calendlyService.handleOAuthCallback(code, state);
        return ResponseEntity.ok("OAuth authentication successful. Access token: " + accessToken);
    }

    /**
     * Webhook endpoint for Calendly events
     */
    @PostMapping("/webhook")
    public ResponseEntity<String> handleWebhook(@RequestBody String payload, @RequestHeader("Calendly-Webhook-Signature") String signature) {
        calendlyService.handleWebhook(payload, signature);
        return ResponseEntity.ok("Webhook processed successfully");
    }

    /**
     * Create a webhook subscription for organization membership events
     */
    @PostMapping("/webhook/create")
    public ResponseEntity<Map<String, Object>> createWebhook(
            @RequestParam String callbackUrl,
            @RequestParam String organizationUri) {

        Map<String, Object> response = new HashMap<>();

        try {
            // Events to subscribe to
            List<String> events = Arrays.asList(
                "organization.membership.created",
                "invitee.created",
                "invitee.canceled"
            );

            String webhookUri = calendlyService.createWebhook(callbackUrl, events, organizationUri);

            response.put("status", "success");
            response.put("message", "Webhook created successfully");
            response.put("webhook_uri", webhookUri);
            response.put("callback_url", callbackUrl);
            response.put("events", events);
            response.put("organization_uri", organizationUri);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to create webhook: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * List existing webhook subscriptions
     */
    @GetMapping("/webhook/list")
    public ResponseEntity<Map<String, Object>> listWebhooks(
            @RequestParam String organizationUri) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<Map<String, Object>> webhooks = calendlyService.listWebhooks(organizationUri);

            response.put("status", "success");
            response.put("message", "Webhooks retrieved successfully");
            response.put("webhooks", webhooks);
            response.put("count", webhooks.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to list webhooks: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
