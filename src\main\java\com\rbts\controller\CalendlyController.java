package com.rbts.controller;

import com.rbts.dto.SessionDTO;
import com.rbts.dto.BookingDTO;
import com.rbts.dto.AvailabilityDTO;
import com.rbts.entity.CalendlyOrganizationMemberDetails;
import com.rbts.dto.calendly.UserAvailabilityScheduleDTO;
import com.rbts.dto.calendly.EventTypeAvailableTimesDTO;
import com.rbts.service.CalendlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.*;

@RestController
@RequestMapping("/api/calendly")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CalendlyController {

    @Autowired
    private CalendlyService calendlyService;

    // 1. CRUD Operations for Session (EventType)
    
    /**
     * Create a new session with Calendly event type
     * @param sessionDTO Session details
     * @param userId Optional user ID for OAuth authentication (if not provided, uses personal access token)
     */
    @PostMapping("/sessions")
    public ResponseEntity<SessionDTO> createSession(
            @Valid @RequestBody SessionDTO sessionDTO,
            @RequestParam(required = false) Long userId) {
        SessionDTO createdSession = calendlyService.createSession(sessionDTO, userId);
        return ResponseEntity.ok(createdSession);
    }


    /**
     * Get session by ID with Calendly event type details
     */
    @GetMapping("/sessions/{id}")
    public ResponseEntity<SessionDTO> getSession(@PathVariable Long id) {
        SessionDTO session = calendlyService.getSession(id);
        return ResponseEntity.ok(session);
    }

    /**
     * Get all sessions for a tutor
     */
    @GetMapping("/sessions/tutor/{tutorId}")
    public ResponseEntity<List<SessionDTO>> getTutorSessions(@PathVariable Long tutorId) {
        List<SessionDTO> sessions = calendlyService.getTutorSessions(tutorId);
        return ResponseEntity.ok(sessions);
    }

    /**
     * Update session and corresponding Calendly event type
     */
    @PutMapping("/sessions/{id}")
    public ResponseEntity<SessionDTO> updateSession(@PathVariable Long id, @Valid @RequestBody SessionDTO sessionDTO) {
        SessionDTO updatedSession = calendlyService.updateSession(id, sessionDTO);
        return ResponseEntity.ok(updatedSession);
    }

    /**
     * Delete session and corresponding Calendly event type
     */
    @DeleteMapping("/sessions/{id}")
    public ResponseEntity<Void> deleteSession(@PathVariable Long id) {
        calendlyService.deleteSession(id);
        return ResponseEntity.noContent().build();
    }

    // 2. Create Organization Invite

    /**
     * Create organization invite for user (only email required)
     */
    @PostMapping("/organization/invite")
    public ResponseEntity<CalendlyOrganizationMemberDetails> createOrganizationInvite(@RequestParam String email) {
        CalendlyOrganizationMemberDetails memberDetails = calendlyService.createOrganizationInvite(email);
        return ResponseEntity.ok(memberDetails);
    }

    /**
     * Check and update organization invite status
     */
    @PostMapping("/organization/invite/check-status")
    public ResponseEntity<CalendlyOrganizationMemberDetails> checkInvitationStatus(@RequestParam String email) {
        CalendlyOrganizationMemberDetails memberDetails = calendlyService.checkAndUpdateInvitationStatus(email);
        return ResponseEntity.ok(memberDetails);
    }



    // 4. Override Availability Calendar
    
    /**
     * Override availability calendar for a user
     */
    @PostMapping("/availability/override")
    public ResponseEntity<String> overrideAvailability(@Valid @RequestBody AvailabilityDTO availabilityDTO) {
        calendlyService.overrideAvailability(availabilityDTO);
        return ResponseEntity.ok("Availability overridden successfully");
    }



    // Calendly Availability APIs

    /**
     * Get user availability schedules from Calendly by user URI
     */
    @GetMapping("/availability/user-schedules")
    public ResponseEntity<UserAvailabilityScheduleDTO> getUserAvailabilitySchedules(@RequestParam String userUri) {
        UserAvailabilityScheduleDTO schedules = calendlyService.getUserAvailabilitySchedules(userUri);
        return ResponseEntity.ok(schedules);
    }

    /**
     * Get user availability schedules from Calendly by email
     */
    @GetMapping("/availability/user-schedules-by-email")
    public ResponseEntity<UserAvailabilityScheduleDTO> getUserAvailabilitySchedulesByEmail(@RequestParam String email) {
        UserAvailabilityScheduleDTO schedules = calendlyService.getUserAvailabilitySchedulesByEmail(email);
        return ResponseEntity.ok(schedules);
    }

    /**
     * Get available times for a specific event type from Calendly
     */
    @GetMapping("/availability/event-type-times")
    public ResponseEntity<EventTypeAvailableTimesDTO> getEventTypeAvailableTimes(
            @RequestParam String eventTypeUri,
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam(defaultValue = "Asia/Kolkata") String timezone) {
        EventTypeAvailableTimesDTO availableTimes = calendlyService.getEventTypeAvailableTimes(eventTypeUri, startTime, endTime, timezone);
        return ResponseEntity.ok(availableTimes);
    }

    // 6. Schedule/Enroll Student for Session
    
    /**
     * Schedule/enroll student for session
     */
    @PostMapping("/bookings")
    public ResponseEntity<BookingDTO> scheduleSession(@Valid @RequestBody BookingDTO bookingDTO) {
        BookingDTO scheduledBooking = calendlyService.scheduleSession(bookingDTO);
        return ResponseEntity.ok(scheduledBooking);
    }

    /**
     * Get booking details
     */
    @GetMapping("/bookings/{bookingId}")
    public ResponseEntity<BookingDTO> getBooking(@PathVariable Long bookingId) {
        BookingDTO booking = calendlyService.getBooking(bookingId);
        return ResponseEntity.ok(booking);
    }

    /**
     * Get all bookings for a student
     */
    @GetMapping("/bookings/student/{studentId}")
    public ResponseEntity<List<BookingDTO>> getStudentBookings(@PathVariable Long studentId) {
        List<BookingDTO> bookings = calendlyService.getStudentBookings(studentId);
        return ResponseEntity.ok(bookings);
    }

    /**
     * Get all bookings for a tutor
     */
    @GetMapping("/bookings/tutor/{tutorId}")
    public ResponseEntity<List<BookingDTO>> getTutorBookings(@PathVariable Long tutorId) {
        List<BookingDTO> bookings = calendlyService.getTutorBookings(tutorId);
        return ResponseEntity.ok(bookings);
    }

    /**
     * Cancel a booking
     */
    @DeleteMapping("/bookings/{bookingId}")
    public ResponseEntity<String> cancelBooking(@PathVariable Long bookingId, @RequestParam(required = false) String reason) {
        calendlyService.cancelBooking(bookingId, reason);
        return ResponseEntity.ok("Booking cancelled successfully");
    }



    // Organization Member Management endpoints

    /**
     * Get member details by email
     */
    @GetMapping("/organization/members/by-email")
    public ResponseEntity<CalendlyOrganizationMemberDetails> getMemberByEmail(@RequestParam String email) {
        Optional<CalendlyOrganizationMemberDetails> member = calendlyService.getMemberByEmail(email);
        return member.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }


}
