package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyScheduledEventResponse {

    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("meeting_notes_plain")
    private String meetingNotesPlain;
    
    @JsonProperty("meeting_notes_html")
    private String meetingNotesHtml;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("start_time")
    private LocalDateTime startTime;
    
    @JsonProperty("end_time")
    private LocalDateTime endTime;
    
    @JsonProperty("event_type")
    private String eventType;
    
    @JsonProperty("location")
    private LocationResponse location;
    
    @JsonProperty("invitees_counter")
    private InviteesCounterResponse inviteesCounter;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("event_memberships")
    private List<EventMembershipResponse> eventMemberships;
    
    @JsonProperty("event_guests")
    private List<EventGuestResponse> eventGuests;
    
    @JsonProperty("calendar_event")
    private CalendarEventResponse calendarEvent;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LocationResponse {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("location")
        private String location;
        
        @JsonProperty("status")
        private String status;
        
        @JsonProperty("join_url")
        private String joinUrl;
        
        @JsonProperty("data")
        private Object data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class InviteesCounterResponse {
        @JsonProperty("total")
        private Integer total;
        
        @JsonProperty("active")
        private Integer active;
        
        @JsonProperty("limit")
        private Integer limit;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EventMembershipResponse {
        @JsonProperty("user")
        private String user;
        
        @JsonProperty("user_email")
        private String userEmail;
        
        @JsonProperty("user_name")
        private String userName;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EventGuestResponse {
        @JsonProperty("email")
        private String email;
        
        @JsonProperty("created_at")
        private LocalDateTime createdAt;
        
        @JsonProperty("updated_at")
        private LocalDateTime updatedAt;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CalendarEventResponse {
        @JsonProperty("kind")
        private String kind;
        
        @JsonProperty("external_id")
        private String externalId;
    }
}
