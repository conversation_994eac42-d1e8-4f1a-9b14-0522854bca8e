package com.rbts.service;

import com.rbts.dto.*;
import com.rbts.dto.calendly.*;
import com.rbts.entity.*;
import com.rbts.entity.master.SessionType;
import com.rbts.entity.master.SubjectMaster;
import com.rbts.entity.master.SubjectSubCategory;
import com.rbts.repository.*;
import com.rbts.config.CalendlyConfig.CalendlyApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CalendlyService {

    @Autowired
    private SessionRepository sessionRepository;
    
    @Autowired
    private BookingRepository bookingRepository;
    
    @Autowired
    private ContactDetailsRepository contactDetailsRepository;
    
    @Autowired
    private BookingOccurrenceRepository bookingOccurrenceRepository;
    
    @Autowired
    private RestTemplate calendlyRestTemplate;

    @Value("${calendly.api.url}")
    private String calendlyApiUrl;

    @Value("${calendly.api.token}")
    private String calendlyApiToken;

    @Value("${calendly.oauth.client-id}")
    private String clientId;

    @Value("${calendly.oauth.client-secret}")
    private String clientSecret;

    @Value("${calendly.oauth.redirect-uri}")
    private String redirectUri;

    @Value("${calendly.webhook.signing-key}")
    private String webhookSigningKey;

    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();

        // Debug logging
        System.out.println("Calendly API Token (first 20 chars): " +
            (calendlyApiToken != null ? calendlyApiToken.substring(0, Math.min(20, calendlyApiToken.length())) + "..." : "NULL"));

        if (calendlyApiToken == null || calendlyApiToken.trim().isEmpty()) {
            throw new RuntimeException("Calendly API token is not configured properly");
        }

        // Use custom Authorization header instead of setBearerAuth (same as Postman)
        headers.set("Authorization", "Bearer " + calendlyApiToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "Locate-Tutor-Service/1.0");

        // Debug logging
        System.out.println("Authorization header: " + headers.get("Authorization"));

        return headers;
    }

    // 1. CRUD Operations for Session (EventType)
    
    @Transactional
    public SessionDTO createSession(SessionDTO sessionDTO) {
        try {
            // Get tutor details
            ContactDetails tutor = contactDetailsRepository.findById(sessionDTO.getTutorId())
                    .orElseThrow(() -> new RuntimeException("Tutor not found with ID: " + sessionDTO.getTutorId()));

            // Create Calendly event type
            Map<String, Object> eventTypeRequest = buildEventTypeRequest(sessionDTO, tutor);

            // Debug logging
            System.out.println("Event Type Request JSON: " + eventTypeRequest);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(eventTypeRequest, getHeaders());

            ResponseEntity<CalendlyEventTypeResponse> response = calendlyRestTemplate.postForEntity(
                    calendlyApiUrl + "/event_types", request, CalendlyEventTypeResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to create Calendly event type");
            }

            CalendlyEventTypeResponse eventType = response.getBody();

            // Create session entity
            Session session = mapToSession(sessionDTO);
            session.setEventTypeUri(eventType.getUri());
            session.setSchedulingUrl(eventType.getSchedulingUrl());
            session.setActive(eventType.isActive());

            session = sessionRepository.save(session);
            return mapToSessionDTO(session, eventType);

        } catch (Exception e) {
            throw new RuntimeException("Failed to create session: " + e.getMessage(), e);
        }
    }

    public SessionDTO getSession(Long id) {
        Session session = sessionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Session not found with ID: " + id));

        if (session.getEventTypeUri() != null) {
            try {
                ResponseEntity<CalendlyEventTypeResponse> response = calendlyRestTemplate.exchange(
                        session.getEventTypeUri(), HttpMethod.GET, new HttpEntity<>(getHeaders()),
                        CalendlyEventTypeResponse.class);
                
                return mapToSessionDTO(session, response.getBody());
            } catch (Exception e) {
                // Return session without Calendly data if API call fails
                return mapToSessionDTO(session, null);
            }
        }
        
        return mapToSessionDTO(session, null);
    }

    public List<SessionDTO> getTutorSessions(Long tutorId) {
        ContactDetails tutor = contactDetailsRepository.findById(tutorId)
                .orElseThrow(() -> new RuntimeException("Tutor not found with ID: " + tutorId));

        List<Session> sessions = sessionRepository.findByTutorId(tutor);
        return sessions.stream()
                .map(session -> mapToSessionDTO(session, null))
                .collect(Collectors.toList());
    }

    @Transactional
    public SessionDTO updateSession(Long id, SessionDTO sessionDTO) {
        Session session = sessionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Session not found with ID: " + id));

        try {
            // Update Calendly event type if exists
            if (session.getEventTypeUri() != null) {
                ContactDetails tutor = session.getTutorId();
                Map<String, Object> eventTypeRequest = buildEventTypeRequest(sessionDTO, tutor);
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(eventTypeRequest, getHeaders());
                
                ResponseEntity<CalendlyEventTypeResponse> response = calendlyRestTemplate.exchange(
                        session.getEventTypeUri(), HttpMethod.PUT, request, CalendlyEventTypeResponse.class);

                if (response.getBody() != null) {
                    CalendlyEventTypeResponse eventType = response.getBody();
                    session.setSchedulingUrl(eventType.getSchedulingUrl());
                    session.setActive(eventType.isActive());
                }
            }

            // Update session entity
            updateSessionFromDTO(session, sessionDTO);
            session = sessionRepository.save(session);
            
            return mapToSessionDTO(session, null);

        } catch (Exception e) {
            throw new RuntimeException("Failed to update session: " + e.getMessage(), e);
        }
    }

    @Transactional
    public void deleteSession(Long id) {
        Session session = sessionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Session not found with ID: " + id));

        try {
            // Delete Calendly event type if exists
            if (session.getEventTypeUri() != null) {
                calendlyRestTemplate.exchange(session.getEventTypeUri(), HttpMethod.DELETE,
                        new HttpEntity<>(getHeaders()), Void.class);
            }
        } catch (Exception e) {
            // Log error but continue with local deletion
            System.err.println("Failed to delete Calendly event type: " + e.getMessage());
        }

        sessionRepository.delete(session);
    }

    // 2. Create Organization Invite
    
    @Transactional
    public OrganizationInviteDTO createOrganizationInvite(OrganizationInviteDTO inviteDTO) {
        try {
            Map<String, Object> inviteRequest = new HashMap<>();
            inviteRequest.put("email", inviteDTO.getEmail());
            
            if (inviteDTO.getFirstName() != null || inviteDTO.getLastName() != null) {
                Map<String, String> user = new HashMap<>();
                if (inviteDTO.getFirstName() != null) user.put("first_name", inviteDTO.getFirstName());
                if (inviteDTO.getLastName() != null) user.put("last_name", inviteDTO.getLastName());
                inviteRequest.put("user", user);
            }

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(inviteRequest, getHeaders());
            ResponseEntity<CalendlyInviteResponse> response = calendlyRestTemplate.postForEntity(
                    calendlyApiUrl + "/organizations/invitations", request, CalendlyInviteResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to create organization invite");
            }

            return mapToInviteDTO(response.getBody());

        } catch (Exception e) {
            throw new RuntimeException("Failed to create organization invite: " + e.getMessage(), e);
        }
    }

    public OrganizationInviteDTO getOrganizationInvite(String inviteUri) {
        try {
            ResponseEntity<CalendlyInviteResponse> response = calendlyRestTemplate.exchange(
                    inviteUri, HttpMethod.GET, new HttpEntity<>(getHeaders()), CalendlyInviteResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Invite not found");
            }

            return mapToInviteDTO(response.getBody());

        } catch (Exception e) {
            throw new RuntimeException("Failed to get organization invite: " + e.getMessage(), e);
        }
    }

    // 3. Complete User Registration
    
    @Transactional
    public void completeUserRegistration(Long userId, String inviteUri) {
        try {
            // Verify invite status
            OrganizationInviteDTO invite = getOrganizationInvite(inviteUri);
            
            if (!"accepted".equals(invite.getStatus())) {
                throw new RuntimeException("Invite not accepted. Current status: " + invite.getStatus());
            }

            // Update user registration status
            ContactDetails user = contactDetailsRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
            
            // Assuming there's a field to track registration completion
            // You may need to add this field to ContactDetails entity
            user.setIsVerified(true);
            contactDetailsRepository.save(user);

        } catch (Exception e) {
            throw new RuntimeException("Failed to complete user registration: " + e.getMessage(), e);
        }
    }

    public boolean isUserRegistrationComplete(Long userId) {
        ContactDetails user = contactDetailsRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        return Boolean.TRUE.equals(user.getIsVerified());
    }

    // Helper methods for mapping and building requests
    
    private Map<String, Object> buildEventTypeRequest(SessionDTO sessionDTO, ContactDetails tutor) {
        Map<String, Object> request = new HashMap<>();
        request.put("name", sessionDTO.getTitle());
        request.put("duration", calculateDuration(sessionDTO.getStartTime(), sessionDTO.getEndTime()));
        request.put("kind", sessionDTO.getKind() != null ? sessionDTO.getKind() : "solo");

        if (sessionDTO.getDescription() != null) {
            request.put("description_plain", sessionDTO.getDescription());
        }

        if (sessionDTO.getColor() != null) {
            request.put("color", sessionDTO.getColor());
        }

        // Add the required owner field - this should be the authenticated user's URI
        // For Personal Access Token, we need to get the current user's URI first
        try {
            String userUri = getCurrentUserUri();
            request.put("owner", userUri);
            System.out.println("- Owner URI: " + userUri);
        } catch (Exception e) {
            System.err.println("Failed to get current user URI: " + e.getMessage());
            throw new RuntimeException("Failed to get current user information for event type creation", e);
        }

        // Add debug logging
        System.out.println("Building event type request:");
        System.out.println("- Name: " + sessionDTO.getTitle());
        System.out.println("- Duration: " + calculateDuration(sessionDTO.getStartTime(), sessionDTO.getEndTime()));
        System.out.println("- Kind: " + (sessionDTO.getKind() != null ? sessionDTO.getKind() : "solo"));
        System.out.println("- Description: " + sessionDTO.getDescription());

        return request;
    }

    private String getCurrentUserUri() {
        try {
            ResponseEntity<Map> response = calendlyRestTemplate.exchange(
                    calendlyApiUrl + "/users/me",
                    HttpMethod.GET,
                    new HttpEntity<>(getHeaders()),
                    Map.class
            );

            if (response.getBody() != null && response.getBody().containsKey("resource")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resource = (Map<String, Object>) response.getBody().get("resource");
                return (String) resource.get("uri");
            }

            throw new RuntimeException("Could not retrieve current user URI from Calendly");
        } catch (Exception e) {
            throw new RuntimeException("Failed to get current user information: " + e.getMessage(), e);
        }
    }

    private Integer calculateDuration(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null && endTime != null) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
        return 60; // Default 1 hour
    }

    // 4. Override Availability

    public void overrideAvailability(AvailabilityDTO availabilityDTO) {
        try {
            ContactDetails tutor = contactDetailsRepository.findById(availabilityDTO.getTutorId())
                    .orElseThrow(() -> new RuntimeException("Tutor not found"));

            Map<String, Object> availabilityRequest = buildAvailabilityRequest(availabilityDTO);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(availabilityRequest, getHeaders());

            calendlyRestTemplate.postForEntity(
                    calendlyApiUrl + "/user_availability_schedules", request, Void.class);

        } catch (Exception e) {
            throw new RuntimeException("Failed to override availability: " + e.getMessage(), e);
        }
    }

    public AvailabilityDTO getUserAvailability(Long userId) {
        try {
            ContactDetails user = contactDetailsRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found"));

            // Get user's availability schedule from Calendly
            String url = calendlyApiUrl + "/user_availability_schedules?user=" + user.getEmailId();
            ResponseEntity<CalendlyAvailabilityResponse> response = calendlyRestTemplate.exchange(
                    url, HttpMethod.GET, new HttpEntity<>(getHeaders()), CalendlyAvailabilityResponse.class);

            if (response.getBody() != null && !response.getBody().getCollection().isEmpty()) {
                return mapToAvailabilityDTO(response.getBody().getCollection().get(0));
            }

            return AvailabilityDTO.builder()
                    .tutorId(userId)
                    .timezone("Asia/Kolkata")
                    .build();

        } catch (Exception e) {
            throw new RuntimeException("Failed to get user availability: " + e.getMessage(), e);
        }
    }

    // 5. Get Tutor Availability

    public List<AvailabilityDTO> getTutorAvailability(Long tutorId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            ContactDetails tutor = contactDetailsRepository.findById(tutorId)
                    .orElseThrow(() -> new RuntimeException("Tutor not found"));

            List<Session> sessions = sessionRepository.findByTutorId(tutor);
            List<AvailabilityDTO> allAvailability = new ArrayList<>();

            for (Session session : sessions) {
                if (session.getEventTypeUri() != null) {
                    List<AvailabilityDTO> sessionAvailability = getEventTypeAvailability(
                            session.getEventTypeUri(), startTime, endTime);
                    allAvailability.addAll(sessionAvailability);
                }
            }

            return allAvailability;

        } catch (Exception e) {
            throw new RuntimeException("Failed to get tutor availability: " + e.getMessage(), e);
        }
    }

    public List<AvailabilityDTO> getEventTypeAvailability(String eventTypeUri, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            String url = String.format("%s/event_type_available_times?event_type=%s&start_time=%s&end_time=%s",
                    calendlyApiUrl, eventTypeUri,
                    startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                    endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            ResponseEntity<CalendlyAvailabilityResponse> response = calendlyRestTemplate.exchange(
                    url, HttpMethod.GET, new HttpEntity<>(getHeaders()), CalendlyAvailabilityResponse.class);

            if (response.getBody() != null) {
                return response.getBody().getCollection().stream()
                        .map(this::mapToAvailabilityDTO)
                        .collect(Collectors.toList());
            }

            return new ArrayList<>();

        } catch (Exception e) {
            throw new RuntimeException("Failed to get event type availability: " + e.getMessage(), e);
        }
    }

    // 6. Schedule/Enroll Student

    @Transactional
    public BookingDTO scheduleSession(BookingDTO bookingDTO) {
        try {
            Session session = sessionRepository.findById(bookingDTO.getSessionId())
                    .orElseThrow(() -> new RuntimeException("Session not found"));

            ContactDetails student = contactDetailsRepository.findById(bookingDTO.getStudentId())
                    .orElseThrow(() -> new RuntimeException("Student not found"));

            // Create Calendly scheduled event
            Map<String, Object> schedulingRequest = buildSchedulingRequest(bookingDTO, session, student);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(schedulingRequest, getHeaders());

            ResponseEntity<CalendlyScheduledEventResponse> response = calendlyRestTemplate.postForEntity(
                    calendlyApiUrl + "/scheduled_events", request, CalendlyScheduledEventResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to create scheduled event");
            }

            CalendlyScheduledEventResponse scheduledEvent = response.getBody();

            // Create booking entity
            Booking booking = Booking.builder()
                    .session(session)
                    .students(student)
                    .status(getDefaultStatus()) // You need to implement this
                    .bookedAt(LocalDateTime.now())
                    .creditPointsUsed(bookingDTO.getCreditPointsUsed())
                    .build();

            booking = bookingRepository.save(booking);

            // Create booking occurrence
            BookingOccurrence occurrence = BookingOccurrence.builder()
                    .booking(booking)
                    .occurrenceTime(bookingDTO.getStartTime())
                    .endTime(bookingDTO.getEndTime())
                    .statusId(getDefaultStatus())
                    .build();

            bookingOccurrenceRepository.save(occurrence);

            return mapToBookingDTO(booking, scheduledEvent);

        } catch (Exception e) {
            throw new RuntimeException("Failed to schedule session: " + e.getMessage(), e);
        }
    }

    public BookingDTO getBooking(Long bookingId) {
        Booking booking = bookingRepository.findById(bookingId)
                .orElseThrow(() -> new RuntimeException("Booking not found"));

        return mapToBookingDTO(booking, null);
    }

    public List<BookingDTO> getStudentBookings(Long studentId) {
        List<Booking> bookings = bookingRepository.findByStudentId(studentId);
        return bookings.stream()
                .map(booking -> mapToBookingDTO(booking, null))
                .collect(Collectors.toList());
    }

    public List<BookingDTO> getTutorBookings(Long tutorId) {
        List<Booking> bookings = bookingRepository.findByTutorId(tutorId);
        return bookings.stream()
                .map(booking -> mapToBookingDTO(booking, null))
                .collect(Collectors.toList());
    }

    @Transactional
    public void cancelBooking(Long bookingId, String reason) {
        try {
            Booking booking = bookingRepository.findById(bookingId)
                    .orElseThrow(() -> new RuntimeException("Booking not found"));

            // Cancel in Calendly if there's a scheduled event URI
            // Note: You'd need to store the Calendly event URI in your booking entity

            // Update booking status to cancelled
            StatusMaster cancelledStatus = getCancelledStatus(); // You need to implement this
            booking.setStatus(cancelledStatus);
            bookingRepository.save(booking);

            // Update booking occurrences
            List<BookingOccurrence> occurrences = bookingOccurrenceRepository.findByBooking(booking);
            for (BookingOccurrence occurrence : occurrences) {
                occurrence.setStatusId(cancelledStatus);
                bookingOccurrenceRepository.save(occurrence);
            }

        } catch (Exception e) {
            throw new RuntimeException("Failed to cancel booking: " + e.getMessage(), e);
        }
    }

    // OAuth and Webhook handling

    public String handleOAuthCallback(String code, String state) {
        try {
            Map<String, String> tokenRequest = new HashMap<>();
            tokenRequest.put("grant_type", "authorization_code");
            tokenRequest.put("code", code);
            tokenRequest.put("client_id", clientId);
            tokenRequest.put("client_secret", clientSecret);
            tokenRequest.put("redirect_uri", redirectUri);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(tokenRequest, headers);
            ResponseEntity<Map> response = calendlyRestTemplate.postForEntity(
                    calendlyApiUrl + "/oauth/token", request, Map.class);

            if (response.getBody() != null) {
                return (String) response.getBody().get("access_token");
            }

            throw new RuntimeException("Failed to get access token");

        } catch (Exception e) {
            throw new RuntimeException("OAuth callback failed: " + e.getMessage(), e);
        }
    }

    public void handleWebhook(String payload, String signature) {
        try {
            // Verify webhook signature
            if (!verifyWebhookSignature(payload, signature)) {
                throw new RuntimeException("Invalid webhook signature");
            }

            // Process webhook payload
            // You can parse the payload and handle different event types
            System.out.println("Webhook received: " + payload);

        } catch (Exception e) {
            throw new RuntimeException("Webhook processing failed: " + e.getMessage(), e);
        }
    }

    private boolean verifyWebhookSignature(String payload, String signature) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(webhookSigningKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);

            byte[] hash = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
            String expectedSignature = Base64.getEncoder().encodeToString(hash);

            return signature.equals(expectedSignature);

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            return false;
        }
    }

    // Helper methods for building requests

    private Map<String, Object> buildAvailabilityRequest(AvailabilityDTO availabilityDTO) {
        Map<String, Object> request = new HashMap<>();
        request.put("timezone", availabilityDTO.getTimezone());

        if (availabilityDTO.getDateOverrides() != null) {
            request.put("date_overrides", availabilityDTO.getDateOverrides());
        }

        if (availabilityDTO.getSchedule() != null) {
            request.put("schedule", availabilityDTO.getSchedule());
        }

        return request;
    }

    private Map<String, Object> buildSchedulingRequest(BookingDTO bookingDTO, Session session, ContactDetails student) {
        Map<String, Object> request = new HashMap<>();
        request.put("event_type", session.getEventTypeUri());
        request.put("start_time", bookingDTO.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        request.put("end_time", bookingDTO.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // Add invitee information
        List<Map<String, String>> invitees = new ArrayList<>();
        Map<String, String> invitee = new HashMap<>();
        invitee.put("email", student.getEmailId());
        invitee.put("name", student.getDisplayName());
        invitees.add(invitee);
        request.put("invitees", invitees);

        if (bookingDTO.getAdditionalNotes() != null) {
            request.put("additional_notes", bookingDTO.getAdditionalNotes());
        }

        return request;
    }

    // Helper methods for status management

    private StatusMaster getDefaultStatus() {
        // You need to implement this based on your status management
        // For now, returning null - you should fetch the appropriate status
        return null;
    }

    private StatusMaster getCancelledStatus() {
        // You need to implement this based on your status management
        // For now, returning null - you should fetch the cancelled status
        return null;
    }

    // Mapping methods

    private Session mapToSession(SessionDTO dto) {
        return Session.builder()
                .tutorId(contactDetailsRepository.findById(dto.getTutorId()).orElse(null))
                .sessionType(getSessionType(dto.getSessionTypeId()))
                .title(dto.getTitle())
                .subject(getSubjectMaster(dto.getSubjectId()))
                .subjectSubCategory(dto.getSubjectSubCategoryId() != null ? getSubjectSubCategory(dto.getSubjectSubCategoryId()) : null)
                .description(dto.getDescription())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .sessionPrice(dto.getSessionPrice())
                .currencyCode(dto.getCurrencyCode())
                .minStudents(dto.getMinStudents())
                .maxStudents(dto.getMaxStudents())
                .sessionLocation(getSessionLocation(dto.getSessionLocationId()))
                .active(dto.isActive())
                .build();
    }

    private SessionDTO mapToSessionDTO(Session session, CalendlyEventTypeResponse eventType) {
        SessionDTO.SessionDTOBuilder builder = SessionDTO.builder()
                .id(session.getId())
                .tutorId(session.getTutorId().getId())
                .sessionTypeId(session.getSessionType().getId())
                .title(session.getTitle())
                .subjectId(session.getSubject().getId())
                .subjectSubCategoryId(session.getSubjectSubCategory() != null ? session.getSubjectSubCategory().getId() : null)
                .description(session.getDescription())
                .startTime(session.getStartTime())
                .endTime(session.getEndTime())
                .sessionPrice(session.getSessionPrice())
                .currencyCode(session.getCurrencyCode())
                .minStudents(session.getMinStudents())
                .maxStudents(session.getMaxStudents())
                .sessionLocationId(session.getSessionLocation().getId())
                .eventTypeUri(session.getEventTypeUri())
                .schedulingUrl(session.getSchedulingUrl())
                .active(session.isActive());

        if (eventType != null) {
            builder.eventTypeName(eventType.getName())
                    .duration(eventType.getDuration())
                    .kind(eventType.getKind())
                    .calendlySchedulingUrl(eventType.getSchedulingUrl())
                    .color(eventType.getColor())
                    .internalNote(eventType.getInternalNote())
                    .descriptionPlain(eventType.getDescriptionPlain())
                    .descriptionHtml(eventType.getDescriptionHtml())
                    .secret(eventType.getSecret());
        }

        return builder.build();
    }

    private void updateSessionFromDTO(Session session, SessionDTO dto) {
        if (dto.getTitle() != null) session.setTitle(dto.getTitle());
        if (dto.getDescription() != null) session.setDescription(dto.getDescription());
        if (dto.getStartTime() != null) session.setStartTime(dto.getStartTime());
        if (dto.getEndTime() != null) session.setEndTime(dto.getEndTime());
        if (dto.getSessionPrice() != null) session.setSessionPrice(dto.getSessionPrice());
        if (dto.getCurrencyCode() != null) session.setCurrencyCode(dto.getCurrencyCode());
        if (dto.getMinStudents() != null) session.setMinStudents(dto.getMinStudents());
        if (dto.getMaxStudents() != null) session.setMaxStudents(dto.getMaxStudents());
        session.setActive(dto.isActive());
    }

    private OrganizationInviteDTO mapToInviteDTO(CalendlyInviteResponse response) {
        OrganizationInviteDTO.OrganizationInviteDTOBuilder builder = OrganizationInviteDTO.builder()
                .uri(response.getUri())
                .email(response.getEmail())
                .status(response.getStatus())
                .organization(response.getOrganization())
                .createdAt(response.getCreatedAt())
                .updatedAt(response.getUpdatedAt())
                .lastSentAt(response.getLastSentAt());

        if (response.getUser() != null) {
            OrganizationInviteDTO.UserDTO userDTO = OrganizationInviteDTO.UserDTO.builder()
                    .uri(response.getUser().getUri())
                    .name(response.getUser().getName())
                    .slug(response.getUser().getSlug())
                    .email(response.getUser().getEmail())
                    .schedulingUrl(response.getUser().getSchedulingUrl())
                    .timezone(response.getUser().getTimezone())
                    .avatarUrl(response.getUser().getAvatarUrl())
                    .createdAt(response.getUser().getCreatedAt())
                    .updatedAt(response.getUser().getUpdatedAt())
                    .build();
            builder.user(userDTO);
        }

        return builder.build();
    }

    private AvailabilityDTO mapToAvailabilityDTO(CalendlyAvailabilitySlot availabilitySlot) {
        return AvailabilityDTO.builder()
                .startTime(availabilitySlot.getStartTime())
                .timezone("Asia/Kolkata")
                .status(availabilitySlot.getStatus())
                .build();
    }

    private BookingDTO mapToBookingDTO(Booking booking, CalendlyScheduledEventResponse scheduledEvent) {
        BookingDTO.BookingDTOBuilder builder = BookingDTO.builder()
                .id(booking.getId())
                .sessionId(booking.getSession().getId())
                .studentId(booking.getStudents().getId())
                .statusId(booking.getStatus().getId())
                .bookedAt(booking.getBookedAt())
                .creditPointsUsed(booking.getCreditPointsUsed());

        if (scheduledEvent != null) {
            builder.calendlyUri(scheduledEvent.getUri())
                    .eventName(scheduledEvent.getName())
                    .calendlyStatus(scheduledEvent.getStatus())
                    .startTime(scheduledEvent.getStartTime())
                    .endTime(scheduledEvent.getEndTime())
                    .createdAt(scheduledEvent.getCreatedAt())
                    .updatedAt(scheduledEvent.getUpdatedAt());
        }

        return builder.build();
    }

    // Helper methods for entity lookups

    private SessionType getSessionType(Long sessionTypeId) {
        // You need to implement this with appropriate repository
        // For now, returning a basic SessionType
        return SessionType.builder().id(sessionTypeId).build();
    }

    private SubjectMaster getSubjectMaster(Long subjectId) {
        // You need to implement this with appropriate repository
        // For now, returning a basic SubjectMaster
        return SubjectMaster.builder().id(subjectId).build();
    }

    private SubjectSubCategory getSubjectSubCategory(Long subjectSubCategoryId) {
        // You need to implement this with appropriate repository
        // For now, returning a basic SubjectSubCategory
        return SubjectSubCategory.builder().id(subjectSubCategoryId).build();
    }

    private SessionLocation getSessionLocation(Long sessionLocationId) {
        // You need to implement this with appropriate repository
        // For now, returning a basic SessionLocation
        return SessionLocation.builder().id(sessionLocationId).build();
    }
}
