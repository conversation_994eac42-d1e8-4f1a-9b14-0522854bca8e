package com.rbts.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/calendly/test")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CalendlyTestController {

    @Value("${calendly.api.url}")
    private String calendlyApiUrl;

    @Value("${calendly.api.token}")
    private String calendlyApiToken;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * Test Calendly API connection and token validity
     */
    @GetMapping("/connection")
    public ResponseEntity<Map<String, Object>> testConnection() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Test token by getting current user info
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(calendlyApiToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Void> request = new HttpEntity<>(headers);
            
            ResponseEntity<Map> calendlyResponse = restTemplate.exchange(
                    calendlyApiUrl + "/users/me", 
                    HttpMethod.GET, 
                    request, 
                    Map.class
            );
            
            if (calendlyResponse.getStatusCode() == HttpStatus.OK && calendlyResponse.getBody() != null) {
                Map<String, Object> userInfo = calendlyResponse.getBody();
                
                response.put("status", "success");
                response.put("message", "Calendly API connection successful");
                response.put("token_valid", true);
                response.put("api_url", calendlyApiUrl);
                response.put("token_preview", calendlyApiToken.substring(0, 20) + "...");
                
                // Extract user info from Calendly response
                if (userInfo.containsKey("resource")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> resource = (Map<String, Object>) userInfo.get("resource");
                    response.put("calendly_user", Map.of(
                        "uri", resource.get("uri"),
                        "name", resource.get("name"),
                        "email", resource.get("email"),
                        "scheduling_url", resource.get("scheduling_url"),
                        "timezone", resource.get("timezone")
                    ));
                }
                
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "error");
                response.put("message", "Calendly API returned unexpected response");
                response.put("token_valid", false);
                response.put("http_status", calendlyResponse.getStatusCode().value());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to connect to Calendly API: " + e.getMessage());
            response.put("token_valid", false);
            response.put("error_details", e.getClass().getSimpleName());
            
            // Check if it's an authentication error
            if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                response.put("suggestion", "Token may be invalid or expired. Please check your Personal Access Token.");
            } else if (e.getMessage().contains("403") || e.getMessage().contains("Forbidden")) {
                response.put("suggestion", "Token may not have sufficient permissions. Check token scopes.");
            } else if (e.getMessage().contains("404")) {
                response.put("suggestion", "API endpoint not found. Check the API URL configuration.");
            }
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Test creating a simple event type to verify write permissions
     */
    @PostMapping("/create-test-event-type")
    public ResponseEntity<Map<String, Object>> testCreateEventType() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(calendlyApiToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // Create a simple test event type
            Map<String, Object> eventTypeData = new HashMap<>();
            eventTypeData.put("name", "Test Event Type - " + System.currentTimeMillis());
            eventTypeData.put("duration", 30);
            eventTypeData.put("kind", "solo");
            eventTypeData.put("description_plain", "Test event type created by API");
            
            // Add profile (required)
            Map<String, String> profile = new HashMap<>();
            profile.put("type", "User");
            eventTypeData.put("profile", profile);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(eventTypeData, headers);
            
            ResponseEntity<Map> calendlyResponse = restTemplate.postForEntity(
                    calendlyApiUrl + "/event_types", 
                    request, 
                    Map.class
            );
            
            if (calendlyResponse.getStatusCode() == HttpStatus.CREATED && calendlyResponse.getBody() != null) {
                Map<String, Object> eventTypeInfo = calendlyResponse.getBody();
                
                response.put("status", "success");
                response.put("message", "Test event type created successfully");
                response.put("write_permissions", true);
                
                if (eventTypeInfo.containsKey("resource")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> resource = (Map<String, Object>) eventTypeInfo.get("resource");
                    response.put("event_type", Map.of(
                        "uri", resource.get("uri"),
                        "name", resource.get("name"),
                        "scheduling_url", resource.get("scheduling_url"),
                        "active", resource.get("active")
                    ));
                }
                
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "error");
                response.put("message", "Failed to create test event type");
                response.put("write_permissions", false);
                response.put("http_status", calendlyResponse.getStatusCode().value());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to create test event type: " + e.getMessage());
            response.put("write_permissions", false);
            response.put("error_details", e.getClass().getSimpleName());
            
            if (e.getMessage().contains("422")) {
                response.put("suggestion", "Validation error. Check if all required fields are provided correctly.");
            }
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Get token information and configuration
     */
    @GetMapping("/token-info")
    public ResponseEntity<Map<String, Object>> getTokenInfo() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("api_url", calendlyApiUrl);
        response.put("token_configured", calendlyApiToken != null && !calendlyApiToken.isEmpty());
        response.put("token_preview", calendlyApiToken != null ? calendlyApiToken.substring(0, 20) + "..." : "Not configured");
        response.put("token_type", "Personal Access Token (PAT)");
        response.put("token_format", calendlyApiToken != null && calendlyApiToken.startsWith("eyJ") ? "JWT" : "Unknown");
        
        // Basic token validation
        if (calendlyApiToken != null && calendlyApiToken.startsWith("eyJ")) {
            response.put("token_structure", "Valid JWT format");
            response.put("recommendation", "Token appears to be properly formatted. Test connection to verify validity.");
        } else {
            response.put("token_structure", "Invalid or missing token");
            response.put("recommendation", "Please configure a valid Calendly Personal Access Token.");
        }
        
        return ResponseEntity.ok(response);
    }
}
