package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyOAuthDTO {

    // OAuth Authorization Request
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AuthorizationRequest {
        @JsonProperty("client_id")
        private String clientId;
        
        @JsonProperty("response_type")
        private String responseType; // "code"
        
        @JsonProperty("redirect_uri")
        private String redirectUri;
        
        @JsonProperty("scope")
        private String scope;
        
        @JsonProperty("state")
        private String state; // CSRF protection
    }

    // OAuth Token Request
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TokenRequest {
        @JsonProperty("client_id")
        private String clientId;
        
        @JsonProperty("client_secret")
        private String clientSecret;
        
        @JsonProperty("code")
        private String code;
        
        @JsonProperty("grant_type")
        private String grantType; // "authorization_code"
        
        @JsonProperty("redirect_uri")
        private String redirectUri;
    }

    // OAuth Token Response
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TokenResponse {
        @JsonProperty("access_token")
        private String accessToken;
        
        @JsonProperty("refresh_token")
        private String refreshToken;
        
        @JsonProperty("token_type")
        private String tokenType;
        
        @JsonProperty("scope")
        private String scope;
        
        @JsonProperty("expires_in")
        private Integer expiresIn; // seconds
        
        @JsonProperty("owner")
        private String owner; // Calendly user URI
    }

    // OAuth Refresh Token Request
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RefreshTokenRequest {
        @JsonProperty("client_id")
        private String clientId;
        
        @JsonProperty("client_secret")
        private String clientSecret;
        
        @JsonProperty("refresh_token")
        private String refreshToken;
        
        @JsonProperty("grant_type")
        private String grantType; // "refresh_token"
    }

    // OAuth Revoke Token Request
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RevokeTokenRequest {
        @JsonProperty("client_id")
        private String clientId;
        
        @JsonProperty("client_secret")
        private String clientSecret;
        
        @JsonProperty("token")
        private String token;
    }

    // User Info Response
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserInfo {
        @JsonProperty("uri")
        private String uri;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("email")
        private String email;
        
        @JsonProperty("scheduling_url")
        private String schedulingUrl;
        
        @JsonProperty("timezone")
        private String timezone;
        
        @JsonProperty("current_organization")
        private String currentOrganization;
    }
}
