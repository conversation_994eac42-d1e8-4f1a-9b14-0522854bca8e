package com.rbts.entity;

import com.rbts.entity.master.SettlementCycle;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Table(name = "tutor_settlement_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TutorSettlementConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tutorSettlementCfgSeqGen")
    @SequenceGenerator(name = "tutorSettlementCfgSeqGen", sequenceName = "tutor_settlement_config_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutor;

    @NotNull
    @OneToOne
    @JoinColumn(name = "settlement_cycle", nullable = false)
    private SettlementCycle settlementCycle;

    @Column(name = "payout_method")
    private String payoutMethod;

    @Column(name = "payout_details", columnDefinition = "TEXT")
    private String payoutDetails;
}
