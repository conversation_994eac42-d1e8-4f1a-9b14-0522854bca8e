package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionDTO {

    private Long id;
    
    @NotNull(message = "Tutor ID is required")
    private Long tutorId;
    
    @NotNull(message = "Session type ID is required")
    private Long sessionTypeId;
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotNull(message = "Subject ID is required")
    private Long subjectId;
    
    private Long subjectSubCategoryId;
    
    private String description;
    
    @NotNull(message = "Start time is required")
    private LocalDateTime startTime;
    
    @NotNull(message = "End time is required")
    private LocalDateTime endTime;
    
    @DecimalMin(value = "0.0", inclusive = false, message = "Session price must be greater than 0")
    private BigDecimal sessionPrice;
    
    private String currencyCode;
    
    @Min(value = 1, message = "Minimum students must be at least 1")
    private Long minStudents;
    
    @Min(value = 1, message = "Maximum students must be at least 1")
    private Long maxStudents;
    
    @NotNull(message = "Session location ID is required")
    private Long sessionLocationId;
    
    // Calendly specific fields
    private String eventTypeUri;
    private String ownerUri;
    private String schedulingUrl;
    private boolean active;
    
    // Calendly event type creation fields
    @JsonProperty("name")
    private String eventTypeName;
    
    @JsonProperty("duration")
    private Integer duration; // in minutes
    
    @JsonProperty("kind")
    private String kind = "solo"; // solo, group, collective, round_robin
    
    @JsonProperty("scheduling_url")
    private String calendlySchedulingUrl;
    
    @JsonProperty("color")
    private String color;
    
    @JsonProperty("internal_note")
    private String internalNote;
    
    @JsonProperty("description_plain")
    private String descriptionPlain;
    
    @JsonProperty("description_html")
    private String descriptionHtml;
    
    @JsonProperty("profile")
    private ProfileDTO profile;
    
    @JsonProperty("secret")
    private Boolean secret = false;
    
    @JsonProperty("slug")
    private String slug;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProfileDTO {
        private String name;
        private String owner;
        private String type;
    }
}
