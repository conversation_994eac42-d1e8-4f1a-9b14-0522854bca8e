package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AvailabilityDTO {

    private Long id;
    
    @NotNull(message = "Tutor ID is required")
    private Long tutorId;
    
    @NotNull(message = "Start time is required")
    @JsonProperty("start_time")
    private LocalDateTime startTime;
    
    @NotNull(message = "End time is required")
    @JsonProperty("end_time")
    private LocalDateTime endTime;
    
    @JsonProperty("timezone")
    private String timezone = "Asia/Kolkata";
    
    @JsonProperty("status")
    private String status; // available, busy, tentative
    
    @JsonProperty("event_type")
    private String eventType; // Calendly event type URI
    
    // For availability override
    @JsonProperty("date_overrides")
    private List<DateOverrideDTO> dateOverrides;
    
    @JsonProperty("schedule")
    private ScheduleDTO schedule;
    
    // Response fields from Calendly
    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("default")
    private Boolean isDefault;
    
    @JsonProperty("user")
    private String user; // User URI
    
    @JsonProperty("rules")
    private List<AvailabilityRuleDTO> rules;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DateOverrideDTO {
        @JsonProperty("date")
        private String date; // YYYY-MM-DD format
        
        @JsonProperty("intervals")
        private List<IntervalDTO> intervals;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class IntervalDTO {
        @JsonProperty("from")
        private String from; // HH:MM format
        
        @JsonProperty("to")
        private String to; // HH:MM format
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ScheduleDTO {
        @JsonProperty("timezone")
        private String timezone;
        
        @JsonProperty("intervals")
        private List<WeeklyIntervalDTO> intervals;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WeeklyIntervalDTO {
        @JsonProperty("day")
        private String day; // monday, tuesday, etc.
        
        @JsonProperty("from")
        private String from; // HH:MM format
        
        @JsonProperty("to")
        private String to; // HH:MM format
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AvailabilityRuleDTO {
        @JsonProperty("type")
        private String type; // wday, date
        
        @JsonProperty("intervals")
        private List<IntervalDTO> intervals;
        
        @JsonProperty("wday")
        private String wday; // monday, tuesday, etc.
        
        @JsonProperty("date")
        private String date; // YYYY-MM-DD format
    }
}
