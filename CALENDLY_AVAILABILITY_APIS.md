# 📅 Calendly Availability APIs

## 📋 Overview

This document describes the Calendly availability APIs that fetch user availability schedules and event type available times directly from Calendly's API.

## 🎯 Available APIs

### 1. **User Availability Schedules**
Get a user's availability schedule including working hours, time zones, and date-specific overrides.

### 2. **Event Type Available Times**
Get available time slots for a specific event type within a date range.

## 🚀 API Endpoints

### **1. Get User Availability Schedules by User URI**

```bash
GET /api/calendly/availability/user-schedules?userUri=https://api.calendly.com/users/USER_UUID
```

**Response:**
```json
{
    "collection": [
        {
            "default": true,
            "name": "Working hours",
            "rules": [
                {
                    "type": "wday",
                    "wday": "monday",
                    "intervals": [
                        {
                            "from": "09:00",
                            "to": "17:00"
                        }
                    ]
                },
                {
                    "type": "date",
                    "date": "2025-06-26",
                    "intervals": [
                        {
                            "from": "09:00",
                            "to": "17:00"
                        },
                        {
                            "from": "18:00",
                            "to": "19:00"
                        }
                    ]
                }
            ],
            "timezone": "Asia/Calcutta",
            "uri": "https://api.calendly.com/user_availability_schedules/SCHEDULE_UUID",
            "user": "https://api.calendly.com/users/USER_UUID"
        }
    ]
}
```

### **2. Get User Availability Schedules by Email**

```bash
GET /api/calendly/availability/user-schedules-by-email?email=<EMAIL>
```

**Note:** This endpoint uses the stored `CalendlyOrganizationMemberDetails` to find the user URI by email.

### **3. Get Event Type Available Times**

```bash
GET /api/calendly/availability/event-type-times?eventTypeUri=https://api.calendly.com/event_types/EVENT_UUID&startTime=2025-06-26T00:00:00.000000Z&endTime=2025-06-28T23:59:59.000000Z&timezone=Asia/Kolkata
```

**Parameters:**
- `eventTypeUri`: The Calendly event type URI
- `startTime`: Start time in ISO 8601 format (UTC)
- `endTime`: End time in ISO 8601 format (UTC)
- `timezone`: Timezone (default: Asia/Kolkata)

**Response:**
```json
{
    "collection": [
        {
            "invitees_remaining": 1,
            "scheduling_url": "https://calendly.com/user/event/2025-06-26T05:00:00+00:00",
            "start_time": "2025-06-26T05:00:00Z",
            "status": "available"
        },
        {
            "invitees_remaining": 1,
            "scheduling_url": "https://calendly.com/user/event/2025-06-26T05:30:00+00:00",
            "start_time": "2025-06-26T05:30:00Z",
            "status": "available"
        }
    ]
}
```

## 💻 Usage Examples

### **1. Get User's Working Hours**

```bash
# Using user URI
curl -X GET "http://localhost:9766/api/calendly/availability/user-schedules?userUri=https://api.calendly.com/users/b11f66d8-7b97-4418-8f65-860234719a15"

# Using email (requires user to be in organization)
curl -X GET "http://localhost:9766/api/calendly/availability/user-schedules-by-email?email=<EMAIL>"
```

### **2. Get Available Time Slots for Booking**

```bash
curl -X GET "http://localhost:9766/api/calendly/availability/event-type-times?eventTypeUri=https://api.calendly.com/event_types/5ab2452c-4634-4eb4-9b25-e914c7e5f86e&startTime=2025-06-26T00:00:00.000000Z&endTime=2025-06-28T23:59:59.000000Z&timezone=Asia/Kolkata"
```

## 📊 Response Structure Details

### **User Availability Schedule**

- **default**: Whether this is the user's default schedule
- **name**: Name of the schedule (e.g., "Working hours")
- **rules**: Array of availability rules
  - **type**: "wday" for weekday rules, "date" for specific date overrides
  - **wday**: Day of week (monday, tuesday, etc.)
  - **date**: Specific date (YYYY-MM-DD)
  - **intervals**: Array of time intervals
    - **from**: Start time (HH:MM)
    - **to**: End time (HH:MM)
- **timezone**: User's timezone
- **uri**: Schedule URI
- **user**: User URI

### **Event Type Available Times**

- **invitees_remaining**: Number of spots remaining for this time slot
- **scheduling_url**: Direct URL to book this time slot
- **start_time**: Start time in ISO 8601 format (UTC)
- **status**: Availability status ("available")

## 🔧 Integration with Organization Members

### **Workflow for Organization Members:**

1. **Create organization invite** with email
2. **User accepts invitation** and becomes organization member
3. **Fetch user availability** using email-based endpoint
4. **Get available times** for specific event types
5. **Display booking options** to students

### **Example Integration:**

```java
// 1. Check if user is organization member
Optional<CalendlyOrganizationMemberDetails> member = 
    calendlyService.getMemberByEmail("<EMAIL>");

if (member.isPresent() && "accepted".equals(member.get().getInvitationStatus())) {
    // 2. Get user's availability schedule
    UserAvailabilityScheduleDTO schedule = 
        calendlyService.getUserAvailabilitySchedulesByEmail("<EMAIL>");
    
    // 3. Get available times for specific event type
    EventTypeAvailableTimesDTO availableTimes = 
        calendlyService.getEventTypeAvailableTimes(
            eventTypeUri, 
            "2025-06-26T00:00:00.000000Z", 
            "2025-06-28T23:59:59.000000Z", 
            "Asia/Kolkata"
        );
}
```

## 🎯 Use Cases

1. **Tutor Availability Display**: Show tutor's working hours to students
2. **Booking Interface**: Display available time slots for session booking
3. **Schedule Management**: View and understand user's availability patterns
4. **Time Zone Handling**: Proper timezone conversion for global users
5. **Capacity Management**: Check remaining spots for group sessions

## 🔍 Benefits

- **Real-time Data**: Direct integration with Calendly's live availability
- **Timezone Support**: Proper handling of different timezones
- **Flexible Querying**: Support for both user URI and email-based lookups
- **Detailed Scheduling**: Access to specific time slots and booking URLs
- **Organization Integration**: Seamless integration with member management

This provides comprehensive availability management for your Calendly-integrated tutoring platform! 🚀
