package com.rbts.repository;

import com.rbts.entity.Booking;
import com.rbts.entity.Session;
import com.rbts.entity.ContactDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingRepository extends JpaRepository<Booking, Long> {

    List<Booking> findBySession(Session session);
    
    List<Booking> findByStudents(ContactDetails student);
    
    @Query("SELECT b FROM Booking b WHERE b.session.tutorId.id = :tutorId")
    List<Booking> findByTutorId(@Param("tutorId") Long tutorId);
    
    @Query("SELECT b FROM Booking b WHERE b.students.id = :studentId")
    List<Booking> findByStudentId(@Param("studentId") Long studentId);
    
    @Query("SELECT b FROM Booking b WHERE b.session.id = :sessionId AND b.students.id = :studentId")
    Optional<Booking> findBySessionIdAndStudentId(@Param("sessionId") Long sessionId, @Param("studentId") Long studentId);
    
    @Query("SELECT b FROM Booking b WHERE b.bookedAt >= :startDate AND b.bookedAt <= :endDate")
    List<Booking> findBookingsInDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT b FROM Booking b WHERE b.session.tutorId.id = :tutorId AND b.bookedAt >= :startDate AND b.bookedAt <= :endDate")
    List<Booking> findTutorBookingsInDateRange(@Param("tutorId") Long tutorId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(b) FROM Booking b WHERE b.session.id = :sessionId")
    Long countBookingsBySessionId(@Param("sessionId") Long sessionId);
}
