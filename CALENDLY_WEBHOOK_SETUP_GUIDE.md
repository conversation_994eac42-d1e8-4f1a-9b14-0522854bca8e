# 🔗 Calendly Webhook Setup Guide - Organization Membership Events

## 📋 Overview

This guide shows you how to set up a Calendly webhook that triggers when a user accepts an organization invite (`organization.membership.created`) and automatically updates your contact details using Calendly's official webhook APIs.

## 🎯 What This Webhook Does

When a user accepts your Calendly organization invite:
1. **Triggers**: `organization.membership.created` event
2. **Captures**: User email, Calendly user URI, organization URI, role
3. **Updates**: Contact details in your database with Calendly information
4. **Fetches**: Additional user details from Calendly API (name, scheduling URL, timezone, etc.)

## 🛠️ Setup Steps

### 1. **Database Migration** (Add Calendly Fields to ContactDetails)

The following fields have been added to your `ContactDetails` entity:

```sql
-- Add these columns to your contact_details table
ALTER TABLE contact_details ADD COLUMN calendly_user_uri VARCHAR(255);
ALTER TABLE contact_details ADD COLUMN calendly_organization_uri VARCHAR(255);
ALTER TABLE contact_details ADD COLUMN calendly_role VARCHAR(100);
ALTER TABLE contact_details ADD COLUMN calendly_membership_created_at TIMESTAMP;
ALTER TABLE contact_details ADD COLUMN is_calendly_member BOOLEAN DEFAULT FALSE;
ALTER TABLE contact_details ADD COLUMN calendly_scheduling_url VARCHAR(255);
ALTER TABLE contact_details ADD COLUMN calendly_timezone VARCHAR(100);
ALTER TABLE contact_details ADD COLUMN calendly_avatar_url VARCHAR(255);
```

### 2. **Get Your Organization URI**

First, get your Calendly organization URI:

```bash
curl -X GET "https://api.calendly.com/users/me" \
  -H "Authorization: Bearer YOUR_CALENDLY_TOKEN"
```

Look for the `current_organization` field in the response.

### 3. **Create the Webhook Using Calendly's API**

Use Calendly's official Create Webhook Subscription API:

```bash
curl -X POST "https://api.calendly.com/webhook_subscriptions" \
  -H "Authorization: Bearer YOUR_CALENDLY_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://your-domain.com/api/calendly/webhook",
    "events": [
      "organization.membership.created",
      "invitee.created",
      "invitee.canceled"
    ],
    "organization": "https://api.calendly.com/organizations/YOUR_ORG_UUID",
    "scope": "organization",
    "signing_key": "your_custom_signing_key_here"
  }'
```

**Expected Response**:
```json
{
  "resource": {
    "uri": "https://api.calendly.com/webhook_subscriptions/WEBHOOK_UUID",
    "callback_url": "https://your-domain.com/api/calendly/webhook",
    "created_at": "2025-06-24T10:00:00.000000Z",
    "updated_at": "2025-06-24T10:00:00.000000Z",
    "retry_started_at": null,
    "state": "active",
    "events": [
      "organization.membership.created",
      "invitee.created",
      "invitee.canceled"
    ],
    "scope": "organization",
    "organization": "https://api.calendly.com/organizations/YOUR_ORG_UUID",
    "user": null,
    "group": null
  }
}
```

### 4. **Configure Webhook Signing Key**

Add to your `application.properties`:

```properties
# Webhook signing key (use the same key you provided when creating the webhook)
calendly.webhook.signing-key=your_custom_signing_key_here
```

## 🧪 Testing the Webhook

### 1. **List Existing Webhooks**

Use Calendly's official List Webhook Subscriptions API:

```bash
curl -X GET "https://api.calendly.com/webhook_subscriptions?organization=https://api.calendly.com/organizations/YOUR_ORG_UUID" \
  -H "Authorization: Bearer YOUR_CALENDLY_TOKEN"
```

### 2. **Test Organization Invite Flow**

1. **Create an organization invite** (use your existing invite endpoint)
2. **Accept the invite** (user clicks the invite link and joins)
3. **Check webhook logs** in your application console
4. **Verify contact update** in your database

### 3. **Manual Webhook Test**

You can manually test the webhook with a sample payload (use the actual signing key for signature):

```bash
curl -X POST "http://localhost:9766/api/calendly/webhook" \
  -H "Content-Type: application/json" \
  -H "Calendly-Webhook-Signature: your_calculated_signature" \
  -d '{
    "created_at": "2025-06-24T10:00:00.000000Z",
    "created_by": "https://api.calendly.com/users/USER_UUID",
    "event": "organization.membership.created",
    "payload": {
      "email": "<EMAIL>",
      "organization": "https://api.calendly.com/organizations/ORG_UUID",
      "role": "user",
      "user": "https://api.calendly.com/users/USER_UUID",
      "created_at": "2025-06-24T10:00:00.000000Z",
      "updated_at": "2025-06-24T10:00:00.000000Z"
    }
  }'
```

**Note**: For testing, you may need to temporarily disable signature verification or calculate the correct HMAC-SHA256 signature.

## 📊 Webhook Event Flow

```mermaid
sequenceDiagram
    participant User
    participant Calendly
    participant YourAPI
    participant Database

    User->>Calendly: Accepts organization invite
    Calendly->>YourAPI: POST /webhook (organization.membership.created)
    YourAPI->>YourAPI: Verify webhook signature
    YourAPI->>YourAPI: Parse webhook payload
    YourAPI->>Calendly: GET /users/{user_uri} (fetch user details)
    Calendly->>YourAPI: Return user details
    YourAPI->>Database: Find contact by email
    YourAPI->>Database: Update contact with Calendly info
    YourAPI->>Calendly: Return 200 OK
```

## 🔍 Expected Console Output

When the webhook is triggered, you should see:

```
Webhook received - Event: organization.membership.created
Processing organization.membership.created event
User accepted organization invite:
- Email: <EMAIL>
- User URI: https://api.calendly.com/users/USER_UUID
- Organization URI: https://api.calendly.com/organizations/ORG_UUID
- Role: user
Fetching user details from Calendly: https://api.calendly.com/users/USER_UUID
Fetched user details: {uri=https://api.calendly.com/users/USER_UUID, name=John Doe, email=<EMAIL>, ...}
Updating existing contact with Calendly information
Successfully updated contact with Calendly info: <EMAIL>
```

## 📝 Contact Details Updated

After webhook processing, your contact will have:

- ✅ `calendlyUserUri`: User's Calendly URI
- ✅ `calendlyOrganizationUri`: Organization URI  
- ✅ `calendlyRole`: User's role in organization
- ✅ `calendlyMembershipCreatedAt`: When they joined
- ✅ `isCalendlyMember`: Set to `true`
- ✅ `calendlySchedulingUrl`: User's public scheduling URL
- ✅ `calendlyTimezone`: User's timezone
- ✅ `calendlyAvatarUrl`: User's profile picture
- ✅ `isVerified`: Set to `true` (verified through Calendly)

## 🚨 Troubleshooting

### **Webhook Not Triggering**
1. Check webhook URL is publicly accessible
2. Verify webhook is created successfully
3. Check Calendly webhook logs in their dashboard

### **Signature Verification Failing**
1. Ensure `calendly.webhook.signing-key` is correct
2. Check webhook signature header name
3. Verify signature calculation method

### **Contact Not Found**
1. Check email matching logic
2. Verify contact exists in database
3. Check case sensitivity in email comparison

### **User Details Not Fetched**
1. Verify Calendly API token has correct permissions
2. Check user URI format
3. Ensure API rate limits not exceeded

## ✅ Success Indicators

- [ ] Webhook created successfully
- [ ] Webhook appears in Calendly dashboard
- [ ] Test invite acceptance triggers webhook
- [ ] Contact details updated in database
- [ ] Console logs show successful processing
- [ ] No errors in application logs

## 🎉 Next Steps

Once the webhook is working:

1. **Monitor webhook performance** in production
2. **Add error handling** for edge cases
3. **Set up webhook retry logic** if needed
4. **Add webhook event logging** for audit trail
5. **Consider rate limiting** for webhook endpoints

Your Calendly organization membership webhook is now ready! 🚀
