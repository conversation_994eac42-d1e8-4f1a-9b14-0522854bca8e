package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "calendly_oauth_tokens")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyOAuthToken extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "calendlyOAuthTokenSeqGen")
    @SequenceGenerator(name = "calendlyOAuthTokenSeqGen", sequenceName = "calendly_oauth_token_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId; // Reference to your user system

    @Column(name = "calendly_user_uri", unique = true)
    private String calendlyUserUri; // Calendly user URI

    @Column(name = "access_token", columnDefinition = "TEXT", nullable = false)
    private String accessToken;

    @Column(name = "refresh_token", columnDefinition = "TEXT")
    private String refreshToken;

    @Column(name = "token_type", nullable = false)
    private String tokenType; // Usually "Bearer"

    @Column(name = "scope", columnDefinition = "TEXT")
    private String scope; // Granted scopes

    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "last_refreshed_at")
    private LocalDateTime lastRefreshedAt;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @Column(name = "organization_uri")
    private String organizationUri; // Current organization

    @Column(name = "calendly_user_email")
    private String calendlyUserEmail;

    @Column(name = "calendly_user_name")
    private String calendlyUserName;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (isActive == null) {
            isActive = true;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        if (refreshToken != null) {
            lastRefreshedAt = LocalDateTime.now();
        }
    }

    // Helper methods
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean needsRefresh() {
        // Refresh if token expires within 5 minutes
        return expiresAt != null && LocalDateTime.now().plusMinutes(5).isAfter(expiresAt);
    }
}
