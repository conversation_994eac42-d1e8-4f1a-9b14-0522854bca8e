package com.rbts.entity;

import com.rbts.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "calendly_organization_member_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalendlyOrganizationMemberDetails extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "calendlyOrgMemberSeqGen")
    @SequenceGenerator(name = "calendlyOrgMemberSeqGen", sequenceName = "calendly_org_member_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "organization_uri", nullable = false)
    private String organizationUri;

    @Column(name = "role", nullable = false)
    private String role;

    @Column(name = "invitation_uri", columnDefinition = "TEXT")
    private String invitationUri;

    @Column(name = "invitation_status")
    private String invitationStatus; // pending, accepted, declined

    @Column(name = "user_uri", nullable = false, unique = true)
    private String userUri;

    @Column(name = "user_email", nullable = false, unique = true)
    private String userEmail;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "scheduling_url", columnDefinition = "TEXT", unique = true)
    private String schedulingUrl;

    @Column(name = "slug", unique = true)
    private String slug;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "locale")
    private String locale;

    @Column(name = "time_notation")
    private String timeNotation;

    @Column(name = "active", nullable = false)
    private boolean active;

    @OneToOne
    @JoinColumn(name = "contact_id", unique = true)
    private ContactDetails contact;
}
