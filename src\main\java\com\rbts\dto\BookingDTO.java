package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BookingDTO {

    private Long id;
    
    @NotNull(message = "Session ID is required")
    private Long sessionId;
    
    @NotNull(message = "Student ID is required")
    private Long studentId;
    
    @NotNull(message = "Status ID is required")
    private Long statusId;
    
    private LocalDateTime bookedAt;
    
    @Min(value = 0, message = "Credit points used cannot be negative")
    private Long creditPointsUsed;
    
    private Long recurrencePatternId;
    
    // Calendly specific fields for scheduling
    @NotNull(message = "Start time is required")
    @JsonProperty("start_time")
    private LocalDateTime startTime;
    
    @NotNull(message = "End time is required")
    @JsonProperty("end_time")
    private LocalDateTime endTime;
    
    @JsonProperty("event_type")
    private String eventType; // Calendly event type URI
    
    @JsonProperty("invitees")
    private List<InviteeDTO> invitees;
    
    @JsonProperty("timezone")
    private String timezone = "Asia/Kolkata";
    
    @JsonProperty("location")
    private LocationDTO location;
    
    @JsonProperty("additional_notes")
    private String additionalNotes;
    
    @JsonProperty("tracking")
    private TrackingDTO tracking;
    
    // Response fields from Calendly
    @JsonProperty("uri")
    private String calendlyUri;
    
    @JsonProperty("name")
    private String eventName;
    
    @JsonProperty("status")
    private String calendlyStatus;
    
    @JsonProperty("booking_method")
    private String bookingMethod;
    
    @JsonProperty("invitees_counter")
    private InviteesCounterDTO inviteesCounter;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class InviteeDTO {
        private String email;
        private String name;
        
        @JsonProperty("first_name")
        private String firstName;
        
        @JsonProperty("last_name")
        private String lastName;
        
        @JsonProperty("timezone")
        private String timezone;
        
        @JsonProperty("text_reminder_number")
        private String textReminderNumber;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LocationDTO {
        private String type; // physical, phone_call, google_meet, zoom, etc.
        private String location;
        
        @JsonProperty("additional_info")
        private String additionalInfo;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TrackingDTO {
        @JsonProperty("utm_campaign")
        private String utmCampaign;
        
        @JsonProperty("utm_source")
        private String utmSource;
        
        @JsonProperty("utm_medium")
        private String utmMedium;
        
        @JsonProperty("utm_content")
        private String utmContent;
        
        @JsonProperty("utm_term")
        private String utmTerm;
        
        @JsonProperty("salesforce_uuid")
        private String salesforceUuid;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class InviteesCounterDTO {
        private Integer total;
        private Integer limit;
        private Integer active;
    }
}
